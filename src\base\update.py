import requests
import sys
import os
import src.base.settings as setting

class update:
    def __init__(self):
        pass
    @staticmethod
    def downloadFile():
        url="https://cscec3b-fip.hb11oss.ctyunxs.cn/ftools.zip"
        path=os.path.dirname(sys.executable)+"/ftools.zip"
        r = requests.get(url)
        if r.status_code==200:
            with open(path,'wb') as f:
                f.write(r.content)
            #便利指定目录删除以index开头的文件 第一个assets
            if not os.path.exists(setting.PATH_INTERNAL+"/web/assets"):
                os.makedirs(setting.PATH_INTERNAL+"/web/assets")
            if not os.path.exists(setting.PATH_INTERNAL+"/web/js"):
                os.makedirs(setting.PATH_INTERNAL+"/web/js")
            for file in os.listdir(setting.PATH_INTERNAL+"/web/assets"):
                os.remove(setting.PATH_INTERNAL+"/web/assets/"+file)
            for file in os.listdir(setting.PATH_INTERNAL+"/web/js"):
                os.remove(setting.PATH_INTERNAL+"/web/js/"+file)
            url="https://cscec3b-fip.hb11oss.ctyunxs.cn/web.zip" #更新web
            path=setting.PATH_INTERNAL+"/web/web.zip"
            print("更新web")
            r = requests.get(url)
            if r.status_code==200:
                with open(path,'wb') as f:
                    f.write(r.content)
            import zipfile
            if os.path.exists(path):
                with zipfile.ZipFile(path, 'r') as z:
                    z.extractall(setting.PATH_INTERNAL+"/web")
                os.remove(path)
        else:
            print("web更新失败")
            return

        if False:
            url="https://cscec3b-fip.hb11oss.ctyunxs.cn/web_assets.zip" #更新web，修改到系统维护里面啦
            path=setting.PATH_INTERNAL+"/web/web_assets.zip"
            r = requests.get(url)
            with open(path,'wb') as f:
                f.write(r.content)
            import zipfile
            with zipfile.ZipFile(path, 'r') as z:
                z.extractall(setting.PATH_INTERNAL+"/web/assets")
            os.remove(path)

        print("更新完成，请重启")
