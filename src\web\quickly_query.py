from pydantic import BaseModel
from typing import Dict, Any, List
import duckdb
from datetime import datetime
import src.base.settings as settings
from fastapi import HTT<PERSON>Ex<PERSON>, Request
from src.web.http import app  # Import the FastAPI app instance
from typing import Any, Optional
import sqlite3
import json

# 数据库连接
DB_PATH =settings.PATH_DUCKDB+'/example.duckdb'

# 请求模型
class QueryRequest(BaseModel):
    filters: Dict[str, Any]
    timestamp: str

# 响应模型
class QueryResponse(BaseModel):
    code: int
    message: str
    data: List[List[Any]]
    timestamp: str


def build_where_clause(filters: Dict[str, Any], table_name: str) -> str:
    """构建WHERE子句"""
    conditions = []
    
    for key, value in filters.items():
        if not value:
            continue
            
        # 处理金额范围查询
        if key.endswith('Min') and value:
            field_name = key[:-3]  # 移除'Min'后缀
            conditions.append(f"{field_name} >= {float(value)}")
        elif key.endswith('Max') and value:
            field_name = key[:-3]  # 移除'Max'后缀
            conditions.append(f"{field_name} <= {float(value)}")
        # 处理日期范围查询
        elif isinstance(value, list) and len(value) == 2:
            if value[0] and value[1]:
                conditions.append(f"{key} BETWEEN '{value[0]}' AND '{value[1]}'")
        # 处理文本查询
        elif isinstance(value, str) and value.strip():
            conditions.append(f"{key} LIKE '%{value.strip()}%'")
        # 处理数字查询
        elif isinstance(value, (int, float)):
            conditions.append(f"{key} = {value}")
    
    return " AND ".join(conditions) if conditions else "1=1"


@app.post("/api/query/integrated-contract", response_model=QueryResponse)
async def query_integrated_contract(request: QueryRequest):
    """查询一体化合同台账"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        where_clause = build_where_clause(request.filters, "一体化合同台账")
        query = f"""
            SELECT * FROM 一体化合同台账 
            WHERE {where_clause}
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        # 构造返回数据，第一行为列名
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/special-reserve", response_model=QueryResponse)
async def query_special_reserve(request: QueryRequest):
    """查询专项储备"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        where_clause = build_where_clause(request.filters, "专项储备")
        query = f"""
            SELECT * FROM 专项储备 
            WHERE {where_clause}
            ORDER BY 过帐日期 DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/master-data", response_model=QueryResponse)
async def query_master_data(request: QueryRequest):
    """查询主数据"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "主数据")
        query = f"""
            SELECT * FROM 主数据
            WHERE {where_clause}
            ORDER BY 项目编码
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/payment-ledger", response_model=QueryResponse)
async def query_payment_ledger(request: QueryRequest):
    """查询付款台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "付款台账")
        query = f"""
            SELECT * FROM 付款台账
            WHERE {where_clause}
            ORDER BY 过帐日期 DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/guarantee-ledger", response_model=QueryResponse)
async def query_guarantee_ledger(request: QueryRequest):
    """查询保证金台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "保证金台账")
        query = f"""
            SELECT * FROM 保证金台账
            WHERE {where_clause}
            ORDER BY 截止日期 DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@app.post("/api/query/subject-mapping", response_model=QueryResponse)
async def query_subject_mapping(request: QueryRequest):
    """查询科目映射"""
    try:
        conn = duckdb.connect(DB_PATH)
        where_clause = build_where_clause(request.filters, "科目对照")
        query = f"""
            SELECT * FROM 科目对照
            WHERE {where_clause}
        """
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")


@app.post("/api/update/subject-mapping")
async def update_subject_mapping(request: Request):
    """更新科目映射"""
    try:
        data = await request.json()
        tableName=data["tableName"]
        data=data["data"]
        conn = duckdb.connect(DB_PATH)
        conn.executemany(f"INSERT OR REPLACE INTO {tableName} VALUES (?,?,?,?) ",data)
        conn.close()
        return {
            "code":200,
            "message":"更新成功",
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新失败: {str(e)}")






# 导入额外的API端点
from src.web.additional_endpoints import (
    query_internal_bank,
    query_internal_reconciliation,
    query_subcontractor_settlement,
    query_external_confirmation,
    query_payable_by_supplier,
    query_cost_ledger,
    query_receipt_ledger,
    query_fund_management
)

# 注册额外的API端点
@app.post("/api/query/internal-bank", response_model=QueryResponse)
async def api_query_internal_bank(request: QueryRequest):
    return await query_internal_bank(request)

@app.post("/api/query/internal-reconciliation", response_model=QueryResponse)
async def api_query_internal_reconciliation(request: QueryRequest):
    return await query_internal_reconciliation(request)

@app.post("/api/query/subcontractor-settlement", response_model=QueryResponse)
async def api_query_subcontractor_settlement(request: QueryRequest):
    return await query_subcontractor_settlement(request)

@app.post("/api/query/external-confirmation", response_model=QueryResponse)
async def api_query_external_confirmation(request: QueryRequest):
    return await query_external_confirmation(request)

@app.post("/api/query/payable-by-supplier", response_model=QueryResponse)
async def api_query_payable_by_supplier(request: QueryRequest):
    return await query_payable_by_supplier(request)

@app.post("/api/query/cost-ledger", response_model=QueryResponse)
async def api_query_cost_ledger(request: QueryRequest):
    return await query_cost_ledger(request)

@app.post("/api/query/receipt-ledger", response_model=QueryResponse)
async def api_query_receipt_ledger(request: QueryRequest):
    return await query_receipt_ledger(request)

@app.post("/api/query/fund-management", response_model=QueryResponse)
async def api_query_fund_management(request: QueryRequest):
    return await query_fund_management(request)


@app.post("/api/voucher-query")
async def voucher_query(request: Request):
    try:
        data = await request.json()
        panel_sqls = data.get("panelSQLs", [])

        if not panel_sqls:
            return {
                "success": True,
                "message": "查询成功",
                "data": [[]],  # No data, just headers if any, but it's empty
            }

        # Build CTEs
        ctes = []
        for i, panel_sql in enumerate(panel_sqls):
            original_sql = panel_sql.get("sql", "")
            # Replace the SELECT part to get the required columns for joining.
            # This assumes the original query is in a form like "SELECT * FROM ..."
            # A more robust solution might involve SQL parsing if queries are complex.
            if "*" in original_sql:
                modified_sql = original_sql.replace("*", "DISTINCT 财年, 利润中心, 凭证编号", 1)
            else:
                # Fallback for when there is no '*', assuming the columns are already there.
                # This might need adjustment based on actual incoming SQL queries.
                modified_sql = original_sql
            
            ctes.append(f"query_{i} AS (\n{modified_sql}\n)")

        with_clause = "WITH " + ", \n".join(ctes)

        # Build the final query
        if len(panel_sqls) == 1:
            final_query_logic = "SELECT t0.财年, t0.利润中心, t0.凭证编号 FROM query_0 t0"
        else:
            joins = []
            for i in range(1, len(panel_sqls)):
                joins.append(f"INNER JOIN query_{i} t{i} ON t0.财年 = t{i}.财年 AND t0.利润中心 = t{i}.利润中心 AND t0.凭证编号 = t{i}.凭证编号")
            join_clause = " \n".join(joins)
            final_query_logic = f"""SELECT t0.财年, t0.利润中心, t0.凭证编号
                         FROM query_0 t0
                         {join_clause}"""

        final_sql = f"""{with_clause}
SELECT DISTINCT details.*
FROM 明细帐 details
JOIN (\n{final_query_logic}\n) AS intersection \nON details.财年 = intersection.财年 AND details.利润中心 = intersection.利润中心 AND details.凭证编号 = intersection.凭证编号;"""
        
        conn = duckdb.connect(DB_PATH)
        df = conn.execute(final_sql).df()
        conn.close()
        df=df[[
        "利润中心",
        "利润中心描述",
        "WBS元素",
        "WBS元素描述",
        "凭证编号",
        "总账科目",
        "总账科目长文本",
        "财年",
        "过帐日期",
        "输入日期",
        "输入时间",
        "借贷标识",
        "反记帐",
        "带符号的本位币金额",
        "文本",
        "客户",
        "客户描述",
        "供应商",
        "供应商描述",
        "业务范围描述",
        "地区分类档案文本描述",
        "清帐凭证",
        "清帐日期",
        "中台单据号",
        "合同",
        "合同文本描述",
        "收支项目",
        "收支项目文本描述"
        ]]
        response_data=[df.columns.tolist()]+df.values.tolist()
        return {
            "success": True,
            "message": "查询成功",
            "data": response_data,
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"查询失败: {str(e)}",
            "data": [],
        }
@app.post("/api/voucher-query-single")
async def voucher_query_single(request: Request):
    try:
        data = await request.json()
        panel_sqls = data.get("panelSQLs", [])
        if not panel_sqls:
            return {
                "success": True,
                "message": "查询成功",
                "data": [[]],  # No data, just headers if any, but it's empty
            } 
        sql=panel_sqls[0]["sql"]
        conn = duckdb.connect(DB_PATH)
        df = conn.execute(sql).df()
        conn.close()
        df=df[[
        "利润中心",
        "利润中心描述",
        "WBS元素",
        "WBS元素描述",
        "凭证编号",
        "总账科目",
        "总账科目长文本",
        "财年",
        "过帐日期",
        "输入日期",
        "输入时间",
        "借贷标识",
        "反记帐",
        "带符号的本位币金额",
        "文本",
        "客户",
        "客户描述",
        "供应商",
        "供应商描述",
        "业务范围描述",
        "地区分类档案文本描述",
        "清帐凭证",
        "清帐日期",
        "中台单据号",
        "合同",
        "合同文本描述",
        "收支项目",
        "收支项目文本描述"
        ]]
        response_data=[df.columns.tolist()]+df.values.tolist()
        return {
            "success": True,
            "message": "查询成功",
            "data": response_data,
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"查询失败: {str(e)}",
        } 
@app.post("/api/balance-sheet-query")
async def balance_sheet_query(request: Request):
    try:
        data = await request.json()
        from src.utils.DB.mainDB import mainDB
        db=mainDB()
        result=db.getCloseDataByBalance()[0]
        db.close()
        return {
            "success": True,
            "message": "查询成功",
            "data": result,
        }
    except Exception as e:
        return {
            "success": False,
            "message": f"查询失败: {str(e)}",
        }   

@app.post("/api/detail-ledger-query")  
async def detail_ledger_query(request: Request):  
    try:
        data = await request.json()
        year=int(data["year"])
        month=int(data["month"])
        from src.utils.DB.mainDB import mainDB
        db=mainDB()
        result=db.getCloseComputeByDetailLedger(year,month)[0]
        db.close()
        return {
            "success": True,
            "message": "查询成功",
            "data": result,
        }
    except Exception as e:
        return {
            "success": False,   
            "message": f"查询失败: {str(e)}",
        }   

@app.post("/api/voucher-query-summary")
async def voucher_query_summary(request: Dict[str, Any]):
    """面板汇总查询接口 - 使用UNION合并所有面板结果"""
    try:
        panel_sqls = request.get("panelSQLs", [])
        query_type = request.get("queryType", "union")
        
        if not panel_sqls:
            raise HTTPException(status_code=400, detail="没有提供查询SQL")
        
        conn = duckdb.connect(DB_PATH)
        
        try:
            # 构建UNION查询
            union_parts = []
            dfs = []
            for i,panel_sql in enumerate(panel_sqls):
                sql = panel_sql.get("sql", "")
                sql=sql.replace("*", f" round(sum(带符号的本位币金额),4) as 面板{i+1},any_value(利润中心描述) as 利润中心名称,any_value(WBS元素描述) as 项目名称,any_value(WBS元素) as 项目编码,any_value(利润中心) as 利润中心 ")
                sql=sql+" and WBS元素 not like 'QCQH%' GROUP by 利润中心,WBS元素"
                print(sql)
                df = conn.execute(sql).df()
                dfs.append(df)
            result = dfs[0]
            # 依次与剩余 DataFrame 全连接
            for df in dfs[1:]:
                # 全连接（outer join），以 'id' 为键
                result = result.merge(df, on=['利润中心','项目编码','项目名称','利润中心名称'], how='outer')
            result.fillna(0, inplace=True)
            # 转换为API返回格式
            data = [result.columns.tolist()]+result.values.tolist()
            conn.close()
            return {
                "success": True,
                "data": data,
                "message": f"汇总查询成功，共找到 {len(result)} 条记录"
            }
            
        except Exception as e:
            print(f"汇总查询失败: {str(e)}")
            raise HTTPException(status_code=500, detail=str(e))
            
    except Exception as e:
        print(f"汇总查询失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# SQLite数据库路径（用于存储查询方案）
SCHEMES_DB_PATH = settings.PATH_DUCKDB+r"/query_schemes.db"


# 查询方案模型
class QueryScheme(BaseModel):
    name: str
    description: Optional[str] = ""
    panels: List[Dict[str, Any]]

class QuerySchemeResponse(BaseModel):
    id: int
    name: str
    description: str
    panels: List[Dict[str, Any]]
    created_at: str

# 初始化查询方案数据库
def init_schemes_db():
    """初始化查询方案数据库"""
    conn = sqlite3.connect(SCHEMES_DB_PATH)
    cursor = conn.cursor()
    
    # 创建查询方案表
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS query_schemes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            panels TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    
    conn.commit()
    conn.close()

# 在模块加载时初始化数据库

@app.get("/api/query-schemes")
async def get_query_schemes():
    init_schemes_db()
    """获取所有查询方案"""
    try:
        conn = sqlite3.connect(SCHEMES_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, description, panels, created_at 
            FROM query_schemes 
            ORDER BY created_at DESC
        """)
        
        schemes = []
        for row in cursor.fetchall():
            schemes.append({
                "id": row[0],
                "name": row[1],
                "description": row[2] or "",
                "panels": json.loads(row[3]),
                "createdAt": row[4]
            })
        
        conn.close()
        return schemes
        
    except Exception as e:
        print(f"获取查询方案失败: {str(e)}")
        # 返回模拟数据
        return [
            {
                "id": 1,
                "name": "材料采购查询",
                "description": "查询2024年材料采购相关凭证",
                "panels": [],
                "createdAt": "2024-01-15 10:30:00"
            },
            {
                "id": 2,
                "name": "高额付款查询",
                "description": "查询金额超过10万的付款凭证",
                "panels": [],
                "createdAt": "2024-01-20 14:20:00"
            }
        ]

@app.post("/api/query-schemes")
async def save_query_scheme(scheme: QueryScheme):
    init_schemes_db()
    """保存查询方案"""
    try:
        conn = sqlite3.connect(SCHEMES_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            INSERT INTO query_schemes (name, description, panels) 
            VALUES (?, ?, ?)
        """, (scheme.name, scheme.description, json.dumps(scheme.panels)))
        
        scheme_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return {
            "id": scheme_id,
            "message": "查询方案保存成功"
        }
        
    except Exception as e:
        print(f"保存查询方案失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/query-schemes/{scheme_id}")
async def get_query_scheme(scheme_id: int):
    """获取单个查询方案"""
    try:
        conn = sqlite3.connect(SCHEMES_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, description, panels, created_at 
            FROM query_schemes 
            WHERE id = ?
        """, (scheme_id,))
        
        row = cursor.fetchone()
        conn.close()
        
        if not row:
            raise HTTPException(status_code=404, detail="查询方案不存在")
        
        return {
            "id": row[0],
            "name": row[1],
            "description": row[2] or "",
            "panels": json.loads(row[3]),
            "createdAt": row[4]
        }
        
    except Exception as e:
        print(f"获取查询方案失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/query-schemes/{scheme_id}")
async def delete_query_scheme(scheme_id: int):
    """删除查询方案"""
    try:
        conn = sqlite3.connect(SCHEMES_DB_PATH)
        cursor = conn.cursor()
        
        cursor.execute("DELETE FROM query_schemes WHERE id = ?", (scheme_id,))
        
        if cursor.rowcount == 0:
            conn.close()
            raise HTTPException(status_code=404, detail="查询方案不存在")
        
        conn.commit()
        conn.close()
        
        return {"message": "查询方案删除成功"}
        
    except Exception as e:
        print(f"删除查询方案失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


