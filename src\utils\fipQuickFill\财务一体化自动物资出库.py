import os
import sys
import src.utils.Excel.excel as excel
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
from src.utils.DB.midIntrSQLiteDB import excelDB

def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s

def main():
    ws=excel.myBook().sheet("物资出库")
    table=ws.table("表出库")
    b=int(ws.Cells(1,2).Value)+1
    #ws.UsedRange.Rows.Count+1
    page=browser.myBrowser("cscec").page
    for i in range(b,table.MaxRow+1):
        #先切换项目

        if table.getValue(i,"项目") != table.getValue(i-1,"项目"):
            cscec.changeProjectCscec(page,table.getValue(i,"组织机构"),table.getValue(i,"项目"))

        if table.getValue(i,"类型")=="出库":
            cscec.toFunction(page,"报账系统","物资及资产","出库汇总单-施工行业")

            page.get_by_placeholder("事由不能超过").fill(table.getValue(i,"事由")) #此处还有些区别,查完合同在写事由
            page.get_by_placeholder("事由不能超过").click()
            page.keyboard.type(table.getValue(i,"事由"))

            page.locator(label_question("含原始纸质附件：")).click()
            cscec.getVisible(page,"//div[text()='否']/following-sibling::div[text()='是']").click()

            page.locator("//span[text()='导入']/parent::div/parent::div/parent::div/parent::div").click()
            page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
            with page.expect_file_chooser() as fc_info:
                page.locator("//*[text()='自定义导入']/parent::div/parent::div/parent::div//div[contains(text(),'浏览')]/parent::div/parent::div/parent::div/preceding-sibling::input").click() #点击input才有效果
                file_chooser = fc_info.value
                file_chooser.set_files(thisPath+"/物资调拨附件/"+table.getValue(i,"附件名称"))
                page.locator("//*[text()='自定义导入']/parent::div/parent::div/parent::div//div[text()='导入']").click() 
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click() 
            page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
            cscec.clickDigalog(page,"提示")
            cscec.getVisible(page,"//span[text()='提交']").click()
            cscec.clickDigalog(page,"提示")

            page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #关闭结算单
            
            ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
        
        else:
            cscec.toFunction(page,"报账系统","物资及资产","同法人调拨单（调出）")

            page.locator(label_question("调入项目")).click()
            page.get_by_placeholder("请输入查询关键字").fill(table.getValue(i,"编号"))
            page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
            page.get_by_text(table.getValue(i,"编号")).first.dblclick() #直接双击确定合同

        
            page.get_by_placeholder("事由不能超过").fill(table.getValue(i,"事由"))
            page.get_by_placeholder("事由不能超过").click()
            page.keyboard.type(table.getValue(i,"事由"))

            page.locator(label_question("行业类型")).click()
            page.get_by_text("施工行业",exact=True).click()

            page.locator(label_question("调出日期")).click()
            page.get_by_text("今天",exact=True).click()

            page.locator("//span[text()='导入']/parent::div/parent::div/parent::div/parent::div").first.click()
            page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
            with page.expect_file_chooser() as fc_info:
                page.locator("//*[text()='自定义导入']/parent::div/parent::div/parent::div//div[contains(text(),'浏览')]/parent::div/parent::div/parent::div/preceding-sibling::input").click() #点击input才有效果
                file_chooser = fc_info.value
                file_chooser.set_files(thisPath+"/物资调拨附件/"+table.getValue(i,"附件名称"))
                page.locator("//*[text()='自定义导入']/parent::div/parent::div/parent::div//div[text()='导入']").click() 
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
            page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
            cscec.clickDigalog(page,"提示")
            cscec.getVisible(page,"//span[text()='提交']").click()
            cscec.clickDigalog(page,"提示")
            page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #关闭结算单
            ws.Cells(1,2).Value=ws.Cells(1,2).Value+1
    print("完成任务")

def queryData():
    db=excelDB()
    headers = ['序号','是否','单位','项目','事由','存货类型','金额','用途' ]
    datarows=[1,'是',"中建","第一项目","物资出库","机电材料及设备","1000.00","研发支出"]
    db.queryData("物资快速出库",headers,datarows)
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel("物资快速出库")


