
import src.utils.sapPublic.GetSAPSession as GetSAPSession
import pandas as pd
import openpyxl
import time
from src.utils.financialStatement.complete import getIndexByTitle
from python_calamine import CalamineWorkbook
import openpyxl
import src.base.cache as cache
import src.base.settings as settings
import duckdb
import src.utils.fileui as fileui

#长期应收款，合同资产，应收账款都区分了项目，但是傻逼其他应收款没有

subject_map={
    "合同资产组合认定-已完工未结算款":"0101|组合法-已完工未结算",
    "合同资产组合认定-质保金":"0103|组合法-质保金",
    "应收账款组合认定-其他客户":"0103|其他企业",
    "应收账款组合认定-政府部门及中央企业":"0101|政府部门及中央企业",
    "应收账款组合认定-海外企业":"0102|海外企业",
    "其他应收款组合认定-应收保证金、押金及备用金":"01|组合1：应收保证金、押金及备用金",
    "其他应收款组合认定-应收代垫款":"02|组合2：应收代垫款",
    "其他应收款组合认定-其他":"03|组合3：其他",
}
receivable_map={
    "应收账款\工程款\进度结算":"01|工程款",
    "应收账款\待转销项税额\一般计税":"13|待转销项税额",  
    "其他应收款\保证金\本金":"0700|押金及保证金",
    "其他应收款\押金\非个人押金\本金":"0700|押金及保证金",
    "其他应收款\押金\个人押金\本金":"0700|押金及保证金",
    "其他应收款\外部往来款":"9900|其他",
}

def getTxt(company,group,year,month):
    session = GetSAPSession.creatSAP()
    session.startTransaction("ZARAP0012")
    session.findById("wnd[0]/usr/ctxtS_BUKRS-LOW").text = company
    session.findById("wnd[0]/usr/ctxtP_KHINR").text = group
    session.findById("wnd[0]/usr/txtP_GJAHR").text = year
    session.findById("wnd[0]/usr/txtP_MONAT").text = month
    session.findById("wnd[0]/usr/txtP_MONAT").setFocus()
    session.findById("wnd[0]/usr/txtP_MONAT").caretPosition = 2
    session.findById("wnd[0]/usr/btn%_S_SAKNR_%_APP_%-VALU_PUSH").press()
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV").select()
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV/ssubSCREEN_HEADER:SAPLALDB:3030/tblSAPLALDBSINGLE_E/ctxtRSCSEL_255-SLOW_E[1,0]").text = "1221080000"
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV/ssubSCREEN_HEADER:SAPLALDB:3030/tblSAPLALDBSINGLE_E/ctxtRSCSEL_255-SLOW_E[1,1]").text = "1221060000"
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV/ssubSCREEN_HEADER:SAPLALDB:3030/tblSAPLALDBSINGLE_E/ctxtRSCSEL_255-SLOW_E[1,1]").setFocus()
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV/ssubSCREEN_HEADER:SAPLALDB:3030/tblSAPLALDBSINGLE_E/ctxtRSCSEL_255-SLOW_E[1,1]").caretPosition = 0
    session.findById("wnd[1]/tbar[0]/btn[8]").press()
    session.findById("wnd[0]/tbar[1]/btn[8]").press()
    session.findById("wnd[0]/mbar/menu[0]/menu[1]/menu[0]").select()
    session.findById("wnd[0]/mbar/menu[0]/menu[1]/menu[2]").select()
    session.findById("wnd[1]/tbar[0]/btn[0]").press()
    session.findById("wnd[1]/usr/ctxtDY_PATH").setFocus()
    session.findById("wnd[1]/usr/ctxtDY_PATH").caretPosition = 0
    session.findById("wnd[1]").sendVKey(4)
    session.findById("wnd[2]/usr/ctxtDY_PATH").text = settings.PATH_DOWNLOAD
    session.findById("wnd[2]/usr/ctxtDY_FILENAME").text = "坏账测试.txt"
    session.findById("wnd[2]/usr/ctxtDY_FILENAME").caretPosition = 7
    session.findById("wnd[2]/tbar[0]/btn[0]").press()
    session.findById("wnd[1]/tbar[0]/btn[0]").press()
    session.findById("wnd[0]").close()
    session.findById("wnd[1]/usr/btnSPOP-OPTION1").press()


def ReadTextFileToArray(path):
    # 计算以"|"开头的行数k
    k = 0
    file_lines = []
    with open(path, "r") as file:
        for line in file:
            file_lines.append(line.strip())
    for line in file_lines:
        if line.startswith("|"):
            k += 1
    
    # 根据第4行数据确定列数
    line_data = file_lines[3].split("|")
    columns = len(line_data)
    
    # 创建二维列表，初始化为空字符串
    data_array = [["" for _ in range(columns)] for _ in range(k)]
    
    # 填充数据到二维列表
    k_index = 0
    for line in file_lines:
        if line.startswith("|"):
            line_data = line.split("|")
            for j in range(len(line_data)):
                data_array[k_index][j] = line_data[j]
            k_index += 1
    
    # 数据清洗和转换
    for i in range(len(data_array)):
        for j in range(len(data_array[0])):
            # 去除首尾空格
            data_array[i][j] = data_array[i][j].strip()
            
            # 处理以"-"结尾的负数表示
            if data_array[i][j].endswith("-"):
                try:
                    data_array[i][j] = -float(data_array[i][j][:-1].replace(",", ""))
                except ValueError:
                    pass  # 如果转换失败，保持原值
            
            # 处理包含"币"的列，尝试转换为数值
            if i > 0 and ("币" in data_array[0][j] or "天1" in data_array[0][j] or "坏账计提比例（%）" in data_array[0][j] or "逾期天数" in data_array[0][j] or "账龄" in data_array[0][j]) and type(data_array[i][j]) == str:
                try:
                    data_array[i][j] = float(data_array[i][j].replace(",", ""))
                except (ValueError, TypeError):
                    pass  # 如果转换失败，保持原值 
    return data_array
   
def classify(row):
    if row["认定类型描述"].startswith("其他应收款"):
        if row["天1"]>365:
            if row["到期日"]>"2026-6-30":
                return "长期应收款-长期应收款-信用减值损失"
            else:
                return "长期应收款-一年到期其他非流动资产-信用减值损失"
        else:
            return "其他应收款-信用减值损失"
    elif row["认定类型描述"].startswith("应收账款"):
            return "应收账款-信用减值损失"
    elif row["认定类型描述"].startswith("合同资产"):
        if row["天1"]>365 and "质保金" in row["认定类型描述"]:
            if row["到期日"]>"2026-6-30":
                return "其他非流动资产-合同资产-资产减值损失"
            else:
                return "一年到期其他非流动资产-合同资产-资产减值损失"
        else:
            return "合同资产-合同资产-资产减值损失"
    else:
        return "其他"

def classify_table_name(row):
    if "应收账款" in row["类型"]:
        return "ZC2006 应收账款"
    elif "长期应收款" in row["类型"]:
        return "ZC2027 长期应收款补充披露"
    elif "其他应收款" in row["类型"]:
        return "ZC2113 其他应收款"
    elif "合同资产" in row["类型"]:
        return "ZC2020 合同资产"

def get_last_data(sheet_name,titleList,amountTitle):
    workbook = CalamineWorkbook.from_path(cache.LastYearFinancialReport)
    theList = workbook.get_sheet_by_name(sheet_name).to_python()
    titleCol=[getIndexByTitle(theList,title) for title in titleList]
    amountCol=getIndexByTitle(theList,amountTitle)
    d={}
    for i in range(len(theList)):
        #判断是否为文本整数
        if type(theList[i][0]) is str and theList[i][0].lstrip('-').isdigit() and theList[i][0] != "" and theList[i][0] is not None:
                key=''.join([theList[i][col] if theList[i][col] is not None else '' for col in titleCol])
                if type(theList[i][amountCol]) is not str and theList[i][amountCol] is not None: #如果为空字符则舍弃
                    key2=tuple(theList[i][col] if theList[i][col] is not None else '' for col in titleCol)
                    d[key]=[theList[i][amountCol]+d.get(key,[0])[0],key2]
    return d

def main():
    try:
        data=ReadTextFileToArray(settings.PATH_DOWNLOAD+r"\坏账测试.txt")
        df=pd.DataFrame(data[1:],columns=data[0])
    except:
        path=fileui.select_file()
        df=pd.read_excel(path)
        df.rename(columns={"付款条款":"PayT"},inplace=True)
        df.rename(columns={"业务合作伙伴类型":"类型"},inplace=True)
        df.rename(columns={"净收付到期日":"到期日"},inplace=True)
        df.rename(columns={"天数 1":"天1"},inplace=True)
    
    df["类型"]=df.apply(classify,axis=1)
    df["明细表名"]=df.apply(classify_table_name,axis=1)
    df["计提类别"]=df.apply(lambda row: subject_map.get(row["认定类型描述"],"其他"), axis=1)
    df["应收类别"]=df.apply(lambda row: receivable_map.get(row["科目-坏账计提描述"],"其他"), axis=1)
    conlusion='''
    select 
    any_value(客户) as 客户编码,
    any_value(客户描述) as 客户名称,
    any_value(类型) as 类型,
    any_value(明细表名) as 明细表名,
    any_value(计提类别) as 坏账计提方式,
    any_value(应收类别) as 应收类别,
    any_value(项目描述) as 项目名称,
    sum("应收款项余额(本币)") as 应收金额,
    sum(case when 账龄<1 then "应收款项余额(本币)" else 0 end) as 账龄1年以内金额,
    sum(case when 账龄<2 and 账龄>=1 then "应收款项余额(本币)" else 0 end) as 账龄1到2年金额,
    sum(case when 账龄<3 and 账龄>=2 then "应收款项余额(本币)" else 0 end) as 账龄2到3年金额,
    sum(case when 账龄<4 and 账龄>=3 then "应收款项余额(本币)" else 0 end) as 账龄3到4年金额,
    sum(case when 账龄<5 and 账龄>=4 then "应收款项余额(本币)" else 0 end) as 账龄4到5年金额,
    sum(case when 账龄>=5 then "应收款项余额(本币)" else 0 end) as 账龄5年以上金额,
    sum("应计提坏账(本币)") as 应计提坏账金额,
    sum("已计提坏账(本币)") as 已计提坏账金额,
    sum("本次计提坏账(本币)") as 本次计提坏账金额
    from df
    group by 客户,类型,计提类别,应收类别,项目描述
    '''

    conlusion2='''
    select 
    any_value(客户) as 客户编码,
    any_value(客户描述) as 客户名称,
    any_value(类型) as 类型,
    any_value(明细表名) as 明细表名,
    any_value(计提类别) as 坏账计提方式,
    any_value(应收类别) as 应收类别,
    sum("应收款项余额(本币)") as 应收金额,
    sum(case when 账龄<1 then "应收款项余额(本币)" else 0 end) as 账龄1年以内金额,
    sum(case when 账龄<2 and 账龄>=1 then "应收款项余额(本币)" else 0 end) as 账龄1到2年金额,
    sum(case when 账龄<3 and 账龄>=2 then "应收款项余额(本币)" else 0 end) as 账龄2到3年金额,
    sum(case when 账龄<4 and 账龄>=3 then "应收款项余额(本币)" else 0 end) as 账龄3到4年金额,
    sum(case when 账龄<5 and 账龄>=4 then "应收款项余额(本币)" else 0 end) as 账龄4到5年金额,
    sum(case when 账龄>=5 then "应收款项余额(本币)" else 0 end) as 账龄5年以上金额,
    sum("应计提坏账(本币)") as 应计提坏账金额,
    sum("已计提坏账(本币)") as 已计提坏账金额,
    sum("本次计提坏账(本币)") as 本次计提坏账金额
    from df
    group by 客户,类型,计提类别,应收类别
    '''
    df2=duckdb.sql(conlusion).df()
    df1=duckdb.sql(conlusion2).df()

    #应收账款部分
    应收账款df=df2[df2["明细表名"] == "ZC2006 应收账款"].copy()
    应收账款df = 应收账款df.reset_index(drop=True)
    应收账款df["原值期初数"]=0
    应收账款df["坏账准备期初数"]=0
    d_应收账款=get_last_data("ZC2006 应收账款",["编码","名称","坏账计提方式","项目名称","款项性质"],["原值","期末数"])
    d_应收账款_坏账准备=get_last_data("ZC2006 应收账款",["编码","名称","坏账计提方式","项目名称","款项性质"],["坏账准备","期末数"])
    for i in range(len(应收账款df)):
        key="".join(str(应收账款df.iloc[i][col]) for col in ["客户编码","客户名称","坏账计提方式","项目名称","应收类别"])
        if key in d_应收账款:
            应收账款df.loc[i,"原值期初数"] = d_应收账款[key][0]
            应收账款df.loc[i,"坏账准备期初数"] = d_应收账款_坏账准备[key][0]
            d_应收账款[key]=False
    k=len(应收账款df)
    for key in d_应收账款:
        if d_应收账款[key] is not False:
            newRow={"客户编码":d_应收账款[key][1][0],"客户名称":d_应收账款[key][1][1],"坏账计提方式":d_应收账款[key][1][2],"项目名称":d_应收账款[key][1][3],"应收类别":d_应收账款[key][1][4],"原值期初数":d_应收账款[key][0],"坏账准备期初数":d_应收账款_坏账准备.get(key,[0])[0]}
            应收账款df.loc[k] = newRow
            k+=1
    应收账款df["本年坏账计提数"]=应收账款df["应计提坏账金额"]-应收账款df["坏账准备期初数"]


    #长期应收款部分
    长期应收款df=df2[df2["明细表名"] == "ZC2027 长期应收款补充披露"].copy()
    长期应收款df = 长期应收款df.reset_index(drop=True)
    长期应收款df["原值期初数"]=0
    长期应收款df["原值一年内到期期初数"]=0
    长期应收款df["坏账准备期初数"]=0
    长期应收款df["一年内到期坏账准备期初数"]=0
    d_长期应收款=get_last_data("ZC2027 长期应收款补充披露",["对方单位名称","项目名称"]," 期末原值 ")
    d_长期应收款_一年内到期=get_last_data("ZC2027 长期应收款补充披露",["对方单位名称","项目名称"],"减：一年内到期部分")
    d_长期应收款_坏账准备=get_last_data("ZC2027 长期应收款补充披露",["对方单位名称","项目名称"],[" 坏账准备 ","期末数"])
    d_长期应收款_坏账准备_一年内到期=get_last_data("ZC2027 长期应收款补充披露",["对方单位名称","项目名称"],[" 坏账准备 ","减：一年内到期部分"])
    for i in range(len(长期应收款df)):
        key="".join(str(长期应收款df.iloc[i][col]) for col in ["客户编码","客户名称","项目名称"])
        if key in d_长期应收款:
            长期应收款df.loc[i,"原值期初数"] = d_长期应收款[key][0]
            长期应收款df.loc[i,"坏账准备期初数"] = d_长期应收款_坏账准备[key][0]
            长期应收款df.loc[i,"原值一年内到期期初数"] = d_长期应收款_一年内到期[key][0]
            长期应收款df.loc[i,"一年内到期坏账准备期初数"] = d_长期应收款_坏账准备_一年内到期[key][0]
            d_长期应收款[key]=False
    k=len(长期应收款df)
    for key in d_长期应收款:
        if d_长期应收款[key] is not False:
            newRow={"客户名称":d_长期应收款[key][1][0],"项目名称":d_长期应收款[key][1][1],"原值期初数":d_长期应收款[key][0],"原值一年内到期期初数":d_长期应收款_一年内到期.get(key,[0])[0],"坏账准备期初数":d_长期应收款_坏账准备.get(key,[0])[0],"一年内到期坏账准备期初数":d_长期应收款_坏账准备_一年内到期.get(key,[0])[0]}
            长期应收款df.loc[k] = newRow
            k+=1
    长期应收款df["本年坏账计提数"]=长期应收款df["应计提坏账金额"]-长期应收款df["坏账准备期初数"]

    #其他应收款部分
    其他应收款df=df1[df1["明细表名"] == "ZC2113 其他应收款"].copy()
    其他应收款df = 其他应收款df.reset_index(drop=True)
    其他应收款df["原值期初数"]=0
    其他应收款df["坏账准备期初数"]=0
    d_其他应收款=get_last_data("ZC2113 其他应收款",["编码","名称","坏账计提方式"],["原值","期末数"])
    d_其他应收款_坏账准备=get_last_data("ZC2113 其他应收款",["编码","名称","坏账计提方式"],["坏账准备","期末数"])
    for i in range(len(其他应收款df)):
        key="".join(str(其他应收款df.iloc[i][col]) for col in ["客户编码","客户名称","坏账计提方式"])
        key="#"+key
        if key in d_其他应收款:
            其他应收款df.loc[i,"原值期初数"] = d_其他应收款[key][0]
            其他应收款df.loc[i,"坏账准备期初数"] = d_其他应收款_坏账准备[key][0]
            d_其他应收款[key]=False
    k=len(其他应收款df)
    for key in d_其他应收款:
        if d_其他应收款[key] is not False:
            newRow={"客户编码":d_其他应收款[key][1][0],"客户名称":d_其他应收款[key][1][1],"坏账计提方式":d_其他应收款[key][1][2],"原值期初数":d_其他应收款[key][0],"坏账准备期初数":d_其他应收款_坏账准备.get(key,[0])[0]}
            其他应收款df.loc[k] = newRow
            k+=1
    其他应收款df["本年坏账计提数"]=其他应收款df["应计提坏账金额"]-其他应收款df["坏账准备期初数"]

    #合同资产部分
    合同资产df=df2[df2["明细表名"] == "ZC2020 合同资产"].copy()
    合同资产df = 合同资产df.reset_index(drop=True)
    合同资产df["原值期初数"]=0
    合同资产df["坏账准备期初数"]=0
    d_合同资产=get_last_data("ZC2020 合同资产",["开发商名称","项目名称","合同资产计提类别"],["本年期末-抵消及重分类前","原值"])
    d_合同资产_坏账准备=get_last_data("ZC2020 合同资产",["开发商名称","项目名称","合同资产计提类别"],["本年期末-抵消及重分类前","预期信用损失"])
    for i in range(len(合同资产df)):
        key="".join(str(合同资产df.iloc[i][col]) for col in ["客户编码","客户名称","坏账计提方式"])
        key="#"+key
        if key in d_合同资产:
            合同资产df.loc[i,"原值期初数"] = d_合同资产[key][0]
            合同资产df.loc[i,"坏账准备期初数"] = d_合同资产_坏账准备[key][0]
            d_合同资产[key]=False
    k=len(合同资产df)
    for key in d_合同资产:
        if d_合同资产[key] is not False:
            newRow={"客户编码":d_合同资产[key][1][0],"客户名称":d_合同资产[key][1][1],"坏账计提方式":d_合同资产[key][1][2],"原值期初数":d_合同资产[key][0],"坏账准备期初数":d_合同资产_坏账准备.get(key,[0])[0]}
            合同资产df.loc[k] = newRow
            k+=1
    合同资产df["本年坏账计提数"]=合同资产df["应计提坏账金额"]-合同资产df["坏账准备期初数"]
    path=fileui.select_directory()
    with pd.ExcelWriter(path+"\坏账分析.xlsx") as writer:
        df.to_excel(writer, sheet_name="原始数据", index=False)
        df2.to_excel(writer, sheet_name="一次汇总", index=False)
        应收账款df.to_excel(writer, sheet_name="应收账款明细", index=False)
        长期应收款df.to_excel(writer, sheet_name="长期应收款明细", index=False)
        其他应收款df.to_excel(writer, sheet_name="其他应收款明细", index=False)
        合同资产df.to_excel(writer, sheet_name="合同资产明细", index=False)