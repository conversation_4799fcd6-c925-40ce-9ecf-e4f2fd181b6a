import os
import src.utils.sapPublic.GetSAPSession as GetSAPSession
import src.utils.DB.mainDB as mainDB

def main(year,month):
    session = GetSAPSession.creatSAP()
    session.StartTransaction("ZARAP0003")

    #上载利润中心到剪贴板
    con=mainDB.mainDB()
    params=con.conn.execute("select 利润中心,项目编码 from 主数据").fetchall()
    con.conn.close()
    for i in range(len(params)):
        try:
            print(f"开始{params[i][0]}-{params[i][1]}内部确认")
            session.StartTransaction("ZARAP0003")
            session.findById("wnd[0]/usr/ctxtS_PRCTR-LOW").text = params[i][0]#填写利润中心
            #session.findById("wnd[0]/usr/ctxtS_PSPNR-LOW").text = params[i][1] #填写项目编码
            session.findById("wnd[0]/usr/txtS_GJAHR-LOW").text = year#年份
            session.findById("wnd[0]/usr/txtS_MONAT-LOW").text = month #月份
            session.findById("wnd[0]/usr/radP_DBCL").setFocus()
            session.findById("wnd[0]/usr/radP_DBCL").select()
            session.findById("wnd[0]/usr/radP_QRF").select()
            session.findById("wnd[0]/tbar[1]/btn[8]").press()
            table_RowCount=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").RowCount
            for i in range(table_RowCount):
                session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(i, "ZDBDJZTMS")
                s=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").getCellValue(i, "ZDBDJZTMS").strip()
                if s=="审批中":
                    session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").doubleClickCurrentCell()
                    try:
                        session.findById("wnd[0]/tbar[1]/btn[13]").press()
                        print(session.findById("wnd[0]/tbar[1]/btn[13]").text)
                        sapInfo=session.findById("wnd[0]/sbar").text  
                        print(sapInfo)
                    except:
                        sapInfo="未见确认按钮"
                        print(sapInfo)
                    session.findById("wnd[0]/tbar[0]/btn[3]").press()
            session.findById("wnd[0]/tbar[0]/btn[3]").press()
        except:
            pass



