# 项目名称模糊匹配功能

基于RAG技术的项目名称模糊匹配系统，支持多种相似度计算方法，能够从项目列表中找到最相近的项目名称。

## 功能特点

- **多种匹配算法**: 支持编辑距离、Jaccard相似度、余弦相似度、语义相似度等
- **智能混合匹配**: 结合多种算法的混合方法，提供最佳匹配效果
- **语义理解**: 可选的语义匹配功能（需要sentence-transformers）
- **中文优化**: 针对中文文本进行了优化，支持中文分词
- **高性能**: 支持批量匹配和向量预计算
- **灵活配置**: 可调整相似度阈值、返回结果数量等参数

## 安装依赖

基础功能无需额外依赖，高级功能需要安装以下包：

```bash
pip install sentence-transformers faiss-cpu scikit-learn jieba
```

## 快速开始

### 1. 基础使用

```python
from src.utils.ai.project_matcher import fuzzy_match_project

# 项目列表
projects = [
    "中建三局第一建设工程有限责任公司",
    "中建二局第二建筑工程有限公司", 
    "万科企业股份有限公司",
    "碧桂园控股有限公司"
]

# 模糊匹配
query = "中建三局"
results = fuzzy_match_project(query, projects, top_k=3)

for project_info, similarity in results:
    print(f"{project_info['text']} - 相似度: {similarity:.3f}")
```

### 2. 使用ProjectMatcher类

```python
from src.utils.ai.project_matcher import ProjectMatcher

# 创建匹配器
matcher = ProjectMatcher(use_semantic=True)

# 加载项目列表（支持字典格式）
projects = [
    {"id": "P001", "name": "中建三局项目", "code": "CSCEC3"},
    {"id": "P002", "name": "万科地产开发", "code": "VANKE"}
]
matcher.load_project_list(projects)

# 查找最佳匹配
best_match = matcher.find_best_match("中建项目")
if best_match:
    project_info, similarity = best_match
    print(f"最佳匹配: {project_info['original']['name']}")
```

### 3. 集成的便捷函数

```python
from src.utils.ai.ollama import match_project_name

# 直接使用集成函数
results = match_project_name(
    query="中建项目",
    project_list=projects,
    top_k=5,
    method="hybrid",
    threshold=0.1
)

for result in results:
    print(f"{result['text']} - 相似度: {result['similarity']}")
```

### 4. AI增强匹配

```python
from src.utils.ai.ollama import get_project_suggestions_with_ai

# 结合AI的智能建议
result = get_project_suggestions_with_ai(
    query="建筑工程项目",
    project_list=projects
)

print("模糊匹配结果:", result['fuzzy_matches'])
print("AI建议:", result['ai_suggestion'])
```

## API接口

### HTTP API

```bash
POST /api/project/fuzzy-match
Content-Type: application/json

{
    "query": "中建项目",
    "top_k": 5,
    "method": "hybrid",
    "threshold": 0.1,
    "use_ai": false
}
```

响应：
```json
{
    "query": "中建项目",
    "fuzzy_matches": [
        {
            "text": "中建三局第一建设工程有限责任公司",
            "original": {...},
            "similarity": 0.8567
        }
    ],
    "total_projects": 100
}
```

## 匹配方法说明

- **edit**: 编辑距离相似度，适合拼写错误检测
- **jaccard**: Jaccard相似度，基于词汇重叠
- **cosine**: 余弦相似度，基于TF-IDF向量
- **semantic**: 语义相似度，需要sentence-transformers
- **hybrid**: 混合方法，综合多种算法（推荐）

## 参数配置

- `top_k`: 返回前k个最佳匹配（默认5）
- `threshold`: 相似度阈值，过滤低相似度结果（默认0.0）
- `method`: 匹配方法（默认"hybrid"）
- `use_semantic`: 是否使用语义匹配（默认True）

## 性能优化

1. **预计算向量**: 对于大量项目，使用ProjectMatcher类预计算向量
2. **批量处理**: 使用batch_match方法处理多个查询
3. **阈值过滤**: 设置合适的threshold过滤无关结果
4. **方法选择**: 根据场景选择合适的匹配方法

## 使用场景

- 项目名称自动补全
- 项目信息查询和检索
- 数据清洗和去重
- 智能推荐系统
- 文档分类和标注

## 注意事项

1. 语义匹配功能需要下载预训练模型，首次使用可能较慢
2. 对于大量数据，建议使用ProjectMatcher类进行向量预计算
3. 中文文本建议安装jieba分词库以获得更好效果
4. 可以根据具体业务场景调整相似度阈值

## 测试

运行测试文件验证功能：

```bash
python src/utils/ai/test_project_matcher.py
```

## 扩展开发

可以通过继承ProjectMatcher类来扩展功能：

```python
class CustomProjectMatcher(ProjectMatcher):
    def custom_similarity(self, query, target):
        # 自定义相似度计算逻辑
        pass
```
