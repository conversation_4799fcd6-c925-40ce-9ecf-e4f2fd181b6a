# 电子档案
import flet as ft
from src.Gui.FletGui2.views import ui_constants as C
from src.Gui.FletGui2.components.date_range_components import DateRangeComponent
from src.Gui.FletGui2.components.button import create_execute_button_with_text_row1,create_execute_button_with_text_row2
from src.Gui.FletGui2.components.button import create_execute_button_with_url,create_execute_button_with_dropdown_yes_no
def create_tab1(page: ft.Page):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[
                    
                    create_execute_button_with_text_row2(page,"单据下载","批量下载中台单据-先打开调试模式","打开chrome浏览器","中台单据导出"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_text_row1(page,"批量打印单据","批量打印单据","批量打印单据"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_url(page,"凭证查询","打开web端凭证查询","凭证查询视图","http://127.0.0.1:8000/voucher-query"),
                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )
def create_tab2(page: ft.Page):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[
                    create_execute_button_with_text_row2(page,"期间查询","先打开调试模式","打开chrome浏览器","插入单据脚本"),
                    ft.Divider(height=3, color=ft.colors.with_opacity(1, ft.colors.AMBER_ACCENT_700)),
                    create_execute_button_with_dropdown_yes_no(page,"查询凭证数量",20),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_text_row2(page,"更新档案模版","更新档案模版","查询档案模版","更新档案模版"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_text_row1(page,"批量成册","批量成册-先打开调试模式","执行档案成册"),
                    

                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )

def get_view(page: ft.Page, view_display_name: str, show_home_callback):


    def _handle_return_click(e):
        show_home_callback()

    # Header section
    header = ft.Container(
        content=ft.Row(
            [
                ft.Text(
                    f"{view_display_name}模块",
                    size=24, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON

                ),
                ft.Container(),  # 占位符用于左右分隔
                ft.ElevatedButton(
                    "返回主菜单",
                    icon=ft.icons.ARROW_BACK_IOS_NEW_ROUNDED,
                    on_click=_handle_return_click,
                    style=ft.ButtonStyle(
                        color=ft.colors.WHITE,
                        bgcolor=C.ACCENT_COLOR,
                        padding=ft.padding.symmetric(horizontal=24, vertical=12),
                        shape=ft.RoundedRectangleBorder(radius=8),
                        elevation=3,
                        shadow_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
                    ),
                    height=48
                ),
            ],
            spacing=8,
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            vertical_alignment=ft.CrossAxisAlignment.CENTER
        ),
        padding=ft.padding.only(bottom=20),
        alignment=ft.alignment.center
    )

    # Create tab content for each test case
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="附件管理",
                icon=ft.icons.PAYMENT,
                content=create_tab1(page)
            ),
                        ft.Tab(
                text="成册管理",
                icon=ft.icons.BOOKMARK,
                content=create_tab2(page)
            )
        ],
        expand=True,
        tab_alignment=ft.TabAlignment.CENTER,
        label_color=C.ACCENT_COLOR,
        unselected_label_color=ft.colors.with_opacity(0.7, C.TEXT_COLOR),
        indicator_color=C.ACCENT_COLOR,
        indicator_tab_size=True,
        label_padding=ft.padding.symmetric(horizontal=20, vertical=12),
        # Enhanced tab styling
        overlay_color=ft.colors.with_opacity(0.1, C.ACCENT_COLOR),
        divider_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
        indicator_border_radius=4,
        indicator_padding=ft.padding.symmetric(horizontal=8),
    )

    # Create tabs with improved styling
    
    scrollable_content = ft.Container(
        content=ft.Column(
            [
                header,
                # Tabs container with proper expansion
                ft.Container(
                    content=tabs,
                    expand=True,
                    margin=ft.margin.symmetric(vertical=10),
                )
            ],
            spacing=0,
            expand=True,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
        ),
        expand=True,
        padding=ft.padding.symmetric(horizontal=15, vertical=10)
    )
    
    # Main container with proper constraints
    return ft.Container(
        content=scrollable_content,
        expand=True,
        padding=ft.padding.only(top=10, bottom=10),
        margin=ft.margin.all(0)
    )
