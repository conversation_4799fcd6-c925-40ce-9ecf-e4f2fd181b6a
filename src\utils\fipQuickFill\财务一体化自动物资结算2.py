
from playwright.sync_api import Page
from playwright.sync_api import ChromiumBrowserContext
import time
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.utils.Excel.excel as excel
from src.utils.DB.midIntrSQLiteDB import excelDB
def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s

def autoOne(page:Page,d: dict): 

    page.locator("//*[@id='root']/section/section/aside/div/div/div[2]/div/div[1]/ul[3]/li/span").click()
    page.locator("//span[contains(text(),'应付及付款')]").click()
    page.locator("//span[contains(@class,'txt')][text()='结算单-物资材料']").click() 

    page.get_by_placeholder("事由不允许超过").fill(d["事由"]) 
    page.locator(label_question("合同名称")).click()
    page.get_by_placeholder("请输入查询合同编号关键字").fill(d["合同编号"])
    page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
    page.get_by_text(d["合同编号"]).first.dblclick()

    
    page.locator(label_question("结算类型")).click()
    cscec.getVisible(page,"//div[text()='中间结算']").click()

    page.locator(label_question("含原始纸质附件：")).click()
    cscec.getVisible(page,"//div[text()='否']/following-sibling::div[text()='是']").click()

    page.locator(label_question("当前付款条件")).click()
    ratio=int(d["付款比例"]*100)
    cscec.locatorDigalog(page,"账期信息帮助").locator(f"//*[text()='{ratio}.00%']").dblclick()
    #cscec.getVisible(page,f"//*[text()='{ratio}.00%']").dblclick()




    page.locator(label_question("付款类别")).click()
    page.get_by_placeholder("请输入查询关键字").fill("应付账款-购货款")
    page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
    page.get_by_text("101005").dblclick()
    
    if d["是否调整上期金额"]=="是":
        s="//label[contains(text(),'上期累计结算')]/parent::div/following-sibling::div[1]//input"
        page.locator(s).click()
        page.keyboard.down('Control')
        page.keyboard.press('A')
        page.keyboard.up('Control')
        page.keyboard.press('Backspace')
        page.locator(s).fill("0")
    
    s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[3]"
    page.locator(s).click()
    #page.locator("//div[@class='EI6JOB-v-f']//div//input[@id='undefined-input']/following-sibling::div[1]").click() #存货类型编码选择
    need=page.locator(s).bounding_box()
    x=need['x'] + need['width']/100*95
    y=need['y'] + need['height']/2
    page.mouse.click(x, y)

    page.get_by_placeholder("请输入查询关键字").fill("1001012")
    page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
    page.locator("//div[text()='1001012']/parent::td").dblclick()

    
    s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[7]"
    page.locator(s).click()
    time.sleep(0.1)
    cscec.getInputAbove(page,s).fill("机电材料及设备") #存货名称

    s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[8]"
    page.locator(s).dblclick()
    #s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//div[@class='EI6JOB-t-f']//input"
    time.sleep(0.2)
    cscec.getInputAbove(page,s).fill(str(d["金额"]+d["税额"])) #单价

    s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[9]"
    page.locator(s).dblclick()
    #s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//div[@class='EI6JOB-v-f']//input/following-sibling::div[1]"     
    #page.locator(s).click() #计量单位
    need=page.locator(s).bounding_box()
    x=need['x'] + need['width']/100*95
    y=need['y'] + need['height']/2
    page.mouse.click(x, y)

    page.get_by_placeholder("请输入查询关键字").fill("批")
    page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
    page.get_by_text("批",exact=True).first.dblclick()


    s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[10]"
    page.locator(s).dblclick()
    #s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//div[@class='EI6JOB-t-f']//input"
    cscec.getInputAbove(page,s).fill("1") #数量

    s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[12]"
    page.locator(s).dblclick()
    # s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//div[@class='EI6JOB-v-f']//input/following-sibling::div[1]"
    need=page.locator(s).bounding_box()
    x=need['x'] + need['width']/100*95
    y=need['y'] + need['height']/2
    page.mouse.click(x, y) #发票种类
    if d["计税模式"]=="简易":
        page.get_by_placeholder("请输入查询关键字").fill("增值税普通发票")
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//div[text()='增值税普通发票']/parent::td").dblclick()
        #page.get_by_text("增值税普通发票").first.dblclick()
    else:
        page.get_by_placeholder("请输入查询关键字").fill("增值税专用发票")
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//div[text()='增值税专用发票']/parent::td").dblclick()
        #page.get_by_text("增值税专用发票",exact=True).first.dblclick()有问题
        s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//td[15]"
        page.locator(s).dblclick()
        #s="//div[contains(text(),'存货类型编号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//div[@class='EI6JOB-t-f']//input"
        time.sleep(0.2)
        cscec.getInputAbove(page,s).fill(str(d["税额"])) #专票税额
    
    s="//div[contains(text(),'付款计算方式')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr"
    c_number=page.locator(s).count()
    money=0

    from datetime import datetime , timedelta# 获取当前日期
    current_date = datetime.now()-timedelta(days=10)
    formatted_date = current_date.strftime("%Y-%m-%d") # 格式化日期


    if c_number>2:
        for j in range(1,c_number-1):
            s="//div[contains(text(),'付款计算方式')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr["+str(j)+"]/td[9]/div"
            money=money+float(page.locator(s).text_content().replace(',',''))
        s="//div[contains(text(),'付款计算方式')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr["+str(c_number-1)+"]/td[9]"
        page.locator(s).click()
        cscec.getInputAbove(page,s).fill(str(round(d["金额"]+d["税额"]-money,2))) #调整金额0.01
    for k in range(1,c_number):
        s="//div[contains(text(),'付款计算方式')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr["+str(k)+"]/td[11]"
        page.locator(s).click()
        time.sleep(0.2)
        cscec.getInputAbove(page,s).fill(formatted_date) #批量写入日期

        s="//div[contains(text(),'付款计算方式')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr["+str(k)+"]/td[14]"
        page.locator(s).dblclick()
        time.sleep(0.3)
        cscec.getInputAbove(page,s).fill("10") #批量写入日期

    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
    time.sleep(0.5)
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
    cscec.clickDigalog(page,"处理意见","确定")
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #
   

def autoOperate():
    db=excelDB()
    df=db.getDataframe("物资结算")
    page = browser.myBrowser("cscec").page
    for i,row in df.iterrows():
            if row["是否"]=="是":
                db.updateData("物资结算","是否","开始执行",i+1)
                autoOne(page,row.to_dict())
                db.updateData("批量分包结算","是否","结束执行",i+1)
                

def queryData():
    db=excelDB()
    headers = ["序号","是否","组织机构","项目","事由","原合同编号","一体化合同名称","付款比例","是否调整上期金额","计税模式","原合同名称","供应商名称","金额","税额","含税"]
    db.queryData("物资结算",headers)
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel("物资结算")
