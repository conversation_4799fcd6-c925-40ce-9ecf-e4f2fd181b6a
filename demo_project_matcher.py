"""
项目名称模糊匹配功能演示脚本
可以直接运行此脚本来测试功能
"""

import sys
import os

# 添加src路径到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def demo_basic_matching():
    """演示基础匹配功能"""
    print("=== 项目名称模糊匹配演示 ===\n")
    
    try:
        from src.utils.ai.project_matcher import fuzzy_match_project
        
        # 示例项目列表
        projects = [
            "中建三局第一建设工程有限责任公司",
            "中建二局第二建筑工程有限公司", 
            "中铁建设集团有限公司",
            "万科企业股份有限公司",
            "碧桂园控股有限公司",
            "恒大地产集团有限公司",
            "华润置地有限公司",
            "保利发展控股集团股份有限公司"
        ]
        
        print("项目列表:")
        for i, project in enumerate(projects, 1):
            print(f"  {i}. {project}")
        
        print("\n" + "="*50)
        
        # 测试查询
        test_queries = [
            "中建三局",
            "中建项目", 
            "万科地产",
            "建筑公司",
            "地产开发"
        ]
        
        for query in test_queries:
            print(f"\n🔍 查询: '{query}'")
            print("-" * 30)
            
            try:
                # 使用混合方法进行匹配
                results = fuzzy_match_project(
                    query=query, 
                    projects=projects, 
                    top_k=3, 
                    method="hybrid", 
                    threshold=0.1
                )
                
                if results:
                    print("匹配结果:")
                    for i, (project_info, similarity) in enumerate(results, 1):
                        print(f"  {i}. {project_info['text']}")
                        print(f"     相似度: {similarity:.3f}")
                else:
                    print("  未找到匹配的项目")
                    
            except Exception as e:
                print(f"  匹配出错: {e}")
        
        print("\n" + "="*50)
        print("✅ 基础匹配功能演示完成!")
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保项目路径正确")
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")


def demo_different_methods():
    """演示不同匹配方法的效果"""
    print("\n=== 不同匹配方法对比 ===\n")
    
    try:
        from src.utils.ai.project_matcher import fuzzy_match_project
        
        projects = [
            "中建三局第一建设工程有限责任公司",
            "中建二局第二建筑工程有限公司", 
            "万科企业股份有限公司"
        ]
        
        query = "中建三局"
        methods = ["edit", "jaccard", "cosine", "hybrid"]
        
        print(f"查询: '{query}'")
        print("项目列表: 中建三局、中建二局、万科企业\n")
        
        for method in methods:
            print(f"📊 {method.upper()} 方法:")
            try:
                results = fuzzy_match_project(query, projects, top_k=2, method=method)
                for i, (project_info, similarity) in enumerate(results, 1):
                    # 简化显示项目名称
                    name = project_info['text']
                    if "中建三局" in name:
                        short_name = "中建三局"
                    elif "中建二局" in name:
                        short_name = "中建二局"
                    elif "万科" in name:
                        short_name = "万科企业"
                    else:
                        short_name = name[:10] + "..."
                    
                    print(f"  {i}. {short_name} (相似度: {similarity:.3f})")
            except Exception as e:
                print(f"  ❌ {method}方法出错: {e}")
            print()
        
    except Exception as e:
        print(f"❌ 方法对比演示失败: {e}")


def demo_project_matcher_class():
    """演示ProjectMatcher类的使用"""
    print("=== ProjectMatcher类演示 ===\n")
    
    try:
        from src.utils.ai.project_matcher import ProjectMatcher
        
        # 创建匹配器（不使用语义匹配以避免依赖问题）
        matcher = ProjectMatcher(use_semantic=False)
        
        # 项目列表（字典格式，模拟数据库数据）
        projects = [
            {"id": "P001", "name": "北京CBD核心区建设项目", "code": "BJ-CBD-001"},
            {"id": "P002", "name": "上海浦东新区住宅开发", "code": "SH-PD-002"}, 
            {"id": "P003", "name": "深圳前海金融中心", "code": "SZ-QH-003"},
            {"id": "P004", "name": "广州天河商业综合体", "code": "GZ-TH-004"}
        ]
        
        print("加载项目数据:")
        for project in projects:
            print(f"  {project['code']}: {project['name']}")
        
        # 加载项目列表
        matcher.load_project_list(projects)
        
        print(f"\n✅ 已加载 {len(projects)} 个项目")
        
        # 测试查询
        queries = ["北京CBD", "上海住宅", "深圳金融"]
        
        for query in queries:
            print(f"\n🔍 查询: '{query}'")
            
            # 查找最佳匹配
            best_match = matcher.find_best_match(query, method="hybrid")
            if best_match:
                project_info, similarity = best_match
                original = project_info['original']
                print(f"  最佳匹配: {original['name']}")
                print(f"  项目编码: {original['code']}")
                print(f"  相似度: {similarity:.3f}")
            else:
                print("  未找到匹配项目")
        
        print("\n✅ ProjectMatcher类演示完成!")
        
    except Exception as e:
        print(f"❌ ProjectMatcher演示失败: {e}")


def demo_integrated_functions():
    """演示集成函数的使用"""
    print("\n=== 集成函数演示 ===\n")
    
    try:
        from src.utils.ai.ollama import match_project_name
        
        # 模拟数据库项目数据
        projects = [
            {"id": "001", "name": "中建三局武汉绿地中心", "code": "CSCEC3-WH-001"},
            {"id": "002", "name": "万科深圳前海项目", "code": "VANKE-SZ-002"}, 
            {"id": "003", "name": "碧桂园广州天河综合体", "code": "BGY-GZ-003"},
            {"id": "004", "name": "华润置地北京朝阳项目", "code": "CRL-BJ-004"}
        ]
        
        print("项目数据库:")
        for project in projects:
            print(f"  {project['code']}: {project['name']}")
        
        # 测试查询
        queries = ["中建武汉", "万科深圳", "广州综合体"]
        
        for query in queries:
            print(f"\n🔍 查询: '{query}'")
            
            try:
                results = match_project_name(
                    query=query,
                    project_list=projects,
                    top_k=2,
                    method="hybrid",
                    threshold=0.1
                )
                
                if results:
                    print("  匹配结果:")
                    for i, result in enumerate(results, 1):
                        original = result['original']
                        print(f"    {i}. {original['name']}")
                        print(f"       编码: {original['code']}")
                        print(f"       相似度: {result['similarity']}")
                else:
                    print("  未找到匹配项目")
                    
            except Exception as e:
                print(f"  ❌ 查询出错: {e}")
        
        print("\n✅ 集成函数演示完成!")
        
    except Exception as e:
        print(f"❌ 集成函数演示失败: {e}")


def main():
    """主演示函数"""
    print("🚀 项目名称模糊匹配功能演示")
    print("=" * 60)
    
    try:
        # 基础匹配演示
        demo_basic_matching()
        
        # 不同方法对比
        demo_different_methods()
        
        # ProjectMatcher类演示
        demo_project_matcher_class()
        
        # 集成函数演示
        demo_integrated_functions()
        
        print("\n" + "=" * 60)
        print("🎉 所有演示完成!")
        print("\n📝 使用建议:")
        print("1. 对于简单场景，使用 fuzzy_match_project() 函数")
        print("2. 对于复杂场景，使用 ProjectMatcher 类")
        print("3. 推荐使用 'hybrid' 方法获得最佳效果")
        print("4. 可以调整 threshold 参数过滤低相似度结果")
        print("5. 支持字符串列表和字典列表两种数据格式")
        
        print("\n🔧 API接口:")
        print("POST /api/project/fuzzy-match")
        print("参数: query, top_k, method, threshold, use_ai")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
