from src.utils.Excel.write_data_to_excel import write_list_to_excel
import src.utils.fileui as fileui
import pandas as pd
import sqlite3
import src.base.settings as settings
import duckdb
import os
import requests

def checkPathDownload():
    pathList=["薪酬汇总发放单","薪酬计提(含个税)汇总单","社保汇总计提信息","社保及公积金汇总缴纳单","财务公司支付表"]
    for path in pathList:
        try:
            url=f"https://cscec3b-fip.hb11oss.ctyunxs.cn/{path}.xlsx"
            path2=settings.PATH_EXCEL+"/"+path+".xlsx"
            if not os.path.exists(path2):
                response = requests.get(url)
                if response.status_code==200:
                    with open(path2, 'wb') as f:
                        f.write(response.content)
        except:
            pass

def pushDeclaration(data):
    checkPathDownload()
    def caculatePay(row):
        value=0
        if isinstance(row["本期收入"], (int, float)):
            value=row["本期收入"]
        if isinstance(row["基本养老保险费"], (int, float)):
            value=value-row["基本养老保险费"]
        if isinstance(row["住房公积金"], (int, float)):
            value=value-row["住房公积金"]
        if isinstance(row["基本医疗保险费"], (int, float)):
            value=value-row["基本医疗保险费"]
        if isinstance(row["失业保险费"], (int, float)):
            value=value-row["失业保险费"]
        if isinstance(row["企业(职业)年金"], (int, float)):
            value=value-row["企业(职业)年金"]
        if isinstance(row["其它扣款"], (int, float)):
            value=value-row["其它扣款"]
        if isinstance(row["本次税款"], (int, float)):
            value=value-row["本次税款"]
        return value
    category=data['selectedCategories']
    month=int(data['selectedMonths'][0])
    salaryData=data['taxCalculationData']
    idData=data['idCardData']
    projectData=data['projectMappingData']
    df1=pd.DataFrame(columns=salaryData[0],data=salaryData[1:])
    df1["本次发放"]=df1.apply(lambda row:caculatePay(row),axis=1)
    df2=pd.DataFrame(columns=idData[0],data=idData[1:])
    df3=pd.DataFrame(columns=projectData[0],data=projectData[1:])
    con=duckdb.connect()
    df=con.execute('''SELECT 
    ANY_VALUE(财务标准项目) AS "*项目名称",
    ANY_VALUE(标准项目编码) AS "*项目编号",
    ANY_VALUE(df1.财务标准单位) AS "*组织机构名称",
    ANY_VALUE(财务标准单位编码) AS "*组织机构",
    SUM(本次发放) AS "应付职工薪酬\工资\职工工资",
    SUM(本次税款) AS "个人所得税",
    SUM(本次发放 + 本次税款) AS "应发金额"
FROM df1
LEFT JOIN df3 ON df1.财务标准项目 = df3.标准项目名称
WHERE 月份 = ? AND 薪酬类别 IN ?
GROUP BY 财务标准项目;''',(month,category)).df()
    data=[df.columns.tolist()]+df.values.tolist()
    df4=con.execute("select 银行卡号 as 收款账号,df1.姓名 as 收款人名称,本次发放 as 付款金额,开户行编码 as 联行号 from df1 left join df2 on df1.身份证号=df2.身份证号 where df1.月份=? and df1.薪酬类别 in ?",(month,category)).df()
    savePath=fileui.select_directory("请选择薪酬汇总发放单保存路径")
    path=settings.PATH_EXCEL+"/"+"薪酬汇总发放单.xlsx"
    write_list_to_excel(path,data,newSheetName="薪酬汇总发放单",savePath=savePath)
    path=settings.PATH_EXCEL+"/"+"薪酬计提(含个税)汇总单.xlsx"
    write_list_to_excel(path,data,newSheetName="薪酬计提(含个税)汇总单",savePath=savePath)
    path=settings.PATH_EXCEL+"/"+"财务公司支付表.xlsx"
    data2=[df4.columns.tolist()]+df4.values.tolist()
    write_list_to_excel(path,data2,start_row_range=(1,2),newSheetName="财务公司支付表",savePath=savePath)

def buildFinanceTemplate(data):
    checkPathDownload()
    adjustmentData=data["adjustmentData"]
    projectMappingData=data["projectMappingData"]
    selectedMonth=data["selectedMonth"]
    df1=pd.DataFrame(columns=adjustmentData[0],data=adjustmentData[1:])
    df2=pd.DataFrame(columns=projectMappingData[0],data=projectMappingData[1:])
    #首先提取所有的社保地区，社保缴纳单位，公积金缴纳单位，合成一个set进行去重，然后循环
    filtered_df = df1[df1['月份'].isin(selectedMonth)]
    unique_values = filtered_df.melt(value_vars=['社保缴纳单位', '公积金缴纳单位', '年金缴纳单位'])['value'].drop_duplicates()
    savePath=fileui.select_directory("请选择社保汇总计提信息保存路径")
    for value in unique_values:
        df4=duckdb.sql('''select 
        ANY_VALUE(财务标准项目) AS "*项目名称",
        ANY_VALUE(标准项目编码) AS "*项目编号",
        ANY_VALUE(df1.财务标准单位) AS "*组织机构名称",
        ANY_VALUE(财务标准单位编码) AS "*组织机构",
        SUM(case when df1.社保缴纳单位=$value then 个人养老回摊 else 0 end) AS "应付职工薪酬\工资\社保公积金个人部分\基本养老保险",
        SUM(case when df1.公积金缴纳单位=$value then 个人公积金回摊 else 0 end) AS "应付职工薪酬\工资\社保公积金个人部分\住房公积金",
        SUM(case when df1.社保缴纳单位=$value then 个人医疗回摊 else 0 end) AS "应付职工薪酬\工资\社保公积金个人部分\医疗保险",
        SUM(case when df1.社保缴纳单位=$value then 个人失业回摊 else 0 end) AS "应付职工薪酬\工资\社保公积金个人部分\失业保险",
        SUM(case when df1.社保缴纳单位=$value then 个人年金回摊 else 0 end) AS "应付职工薪酬\工资\社保公积金个人部分\企业年金",
        sum(case when df1.社保缴纳单位=$value then 单位养老分摊 else 0 end) AS "应付职工薪酬\社会保险费单位部分\基本养老保险",
        sum(case when df1.公积金缴纳单位=$value then 单位公积金分摊 else 0 end) AS "应付职工薪酬\住房公积金\基本住房公积金",
        sum(case when df1.社保缴纳单位=$value then 单位医疗分摊 else 0 end) AS "应付职工薪酬\社会保险费单位部分\医疗保险",
        sum(case when df1.社保缴纳单位=$value then 单位失业分摊 else 0 end) AS "应付职工薪酬\社会保险费单位部分\失业保险",
        sum(case when df1.社保缴纳单位=$value then 单位工伤分摊 else 0 end) AS "应付职工薪酬\社会保险费单位部分\工伤保险",
        sum(case when df1.社保缴纳单位=$value then 单位生育分摊 else 0 end) AS "应付职工薪酬\社会保险费单位部分\生育保险",
        sum(case when df1.社保缴纳单位=$value then 补充医疗分摊 else 0 end) AS "应付职工薪酬\社会保险费单位部分\补充医疗保险"
    FROM df1
    LEFT JOIN df2 ON df1.财务标准项目 = df2.标准项目名称
    WHERE 月份 in $month and 薪酬类别='工资'
    GROUP BY 财务标准项目;''',params={"month":selectedMonth,"value":value}).df()
        path=settings.PATH_EXCEL+"/"+"社保汇总计提信息.xlsx"
        data2=[df4.columns.tolist()]+df4.values.tolist()
        write_list_to_excel(path,data2,start_row_range=(1,2),newSheetName=value+"社保汇总计提信息",savePath=savePath)
        path=settings.PATH_EXCEL+"/"+"社保及公积金汇总缴纳单.xlsx"
        write_list_to_excel(path,data2,start_row_range=(1,4),newSheetName=value+"社保及公积金汇总缴纳单",savePath=savePath)
    return True


def socialInsurance_allocate(df:pd.DataFrame,category,category2,month,amount,unit,unitTitle):
    if amount is not None and amount>0.001:
        unitTitle2=unitTitle
        def applyFormula(row,amount,accAmount,month):
            if row['薪酬类别']=='工资' and row[unitTitle2]==unit and row['月份']==month and isinstance(row[category], (int, float)):
                return round(row[category]*amount/accAmount,2)
            else:
                return row[category2]
        accAmount=df[(df['薪酬类别']=='工资') & (df[unitTitle2]==unit) & (df['月份']==month)][category].apply(pd.to_numeric, errors='coerce').sum()
        df[category2]=df.apply(lambda row:applyFormula(row,amount,accAmount,month),axis=1)
        #控制总额一致
        accAmount2=df[(df['薪酬类别']=='工资') & (df[unitTitle2]==unit) & (df['月份']==month)][category2].apply(pd.to_numeric, errors='coerce').sum()
        munus=amount-accAmount2
        if munus>0.001 or munus<-0.001:
            try:
                maxIndex=df[(df['薪酬类别']=='工资') & (df[unitTitle2]==unit) & (df['月份']==month)][category2].idxmax()
                df.loc[maxIndex,category2]=df.loc[maxIndex,category2]+munus
            except:
                pass

def calculateSocialInsurance(df:pd.DataFrame,data:list):
    df['月份']=df['月份'].astype(str)+'月'
    df['个人养老回摊']=0
    df['个人公积金回摊']=0
    df['个人医疗回摊']=0
    df['个人失业回摊']=0
    df['个人年金回摊']=0
    df['补充医疗分摊']=0
    df['单位养老分摊']=0
    df['单位公积金分摊']=0
    df['单位医疗分摊']=0
    df['单位失业分摊']=0
    df['单位工伤分摊']=0
    df['单位生育分摊']=0
    df['单位企业年金分摊']=0
    df['其他分摊']=0
    for i in range(1,len(data)):
        for j in range(2,len(data[i])):
            month=str(data[i][0])+'月'
            if data[0][j]=='个人公积金':
                socialInsurance_allocate(df,'住房公积金','个人公积金回摊',month,data[i][j],data[i][1],"公积金缴纳单位")
            elif data[0][j]=='个人养老':
                socialInsurance_allocate(df,'基本养老保险费','个人养老回摊',month,data[i][j],data[i][1],"社保缴纳单位")
            elif data[0][j]=='个人医疗':
                socialInsurance_allocate(df,'基本医疗保险费','个人医疗回摊',month,data[i][j],data[i][1],"社保缴纳单位")
            elif data[0][j]=='个人失业':
                socialInsurance_allocate(df,'失业保险费','个人失业回摊',month,data[i][j],data[i][1],"社保缴纳单位")
            elif data[0][j]=='个人年金':
                socialInsurance_allocate(df,'企业(职业)年金','个人年金回摊',month,data[i][j],data[i][1],"年金缴纳单位")
            elif data[0][j]=='补充医疗':
                socialInsurance_allocate(df,'本期收入','补充医疗分摊',month,data[i][j],data[i][1],"社保缴纳单位")
            elif data[0][j]=='单位公积金':
                socialInsurance_allocate(df,'住房公积金','单位公积金分摊',month,data[i][j],data[i][1],"公积金缴纳单位")
            elif data[0][j]=='单位养老':
                socialInsurance_allocate(df,'基本养老保险费','单位养老分摊',month,data[i][j],data[i][1],"社保缴纳单位")
            elif data[0][j]=='单位医疗':
                socialInsurance_allocate(df,'基本医疗保险费','单位医疗分摊',month,data[i][j],data[i][1],"社保缴纳单位")
            elif data[0][j]=='单位失业':
                socialInsurance_allocate(df,'失业保险费','单位失业分摊',month,data[i][j],data[i][1],"社保缴纳单位")
            elif data[0][j]=='单位工伤':
                socialInsurance_allocate(df,'本期收入','单位工伤分摊',month,data[i][j],data[i][1],"社保缴纳单位")
            elif data[0][j]=='单位生育':
                socialInsurance_allocate(df,'本期收入','单位生育分摊',month,data[i][j],data[i][1],"社保缴纳单位")
            elif data[0][j]=='企业年金':
                socialInsurance_allocate(df,'企业(职业)年金','单位企业年金分摊',month,data[i][j],data[i][1],"年金缴纳单位")
    new_df=df[["月份","社保缴纳单位","公积金缴纳单位","年金缴纳单位","身份证号", '姓名', '成本所属项目','财务标准项目','财务标准单位', '薪酬类别',
    '本期收入', '个人养老回摊', '个人公积金回摊', '个人医疗回摊',
    '个人失业回摊', '个人年金回摊', '其它扣款', '本次税款',
    '单位养老分摊', '单位公积金分摊', '单位医疗分摊', '单位失业分摊',
    '单位工伤分摊', '单位生育分摊', '补充医疗分摊', '单位企业年金分摊', '其他分摊',
    '备注']]
    new_df=new_df.fillna(0)
    return new_df

def calculate_social_security_allocation(data):
    df=pd.DataFrame(columns=data['taxCalculationData'][0],data=data['taxCalculationData'][1:])
    new_df=calculateSocialInsurance(df,data['socialSecurityData'])
    result=[new_df.columns.tolist()]+new_df.values.tolist()
    return {'code':200,'data':result}
    
def saveAllTax(data):
    conn = sqlite3.connect(settings.PATH_DUCKDB+'/salay.db')
    year=data['year']
    #获取第二个到倒数第二个的字典里面的key
    #获取key
    keys = list(data.keys())
    
    # 如果字典元素少于3个，无法提取第二个到倒数第二个元素
    if len(keys) <= 2:
        print("字典元素少于3个，无法提取第二个到倒数第二个元素")
        return
    
    # 切片获取第二个到倒数第二个键
    target_keys = keys[1:-1]
    for key in target_keys:
        if key!='socialSecurityAndHousingFundAdjustment':
            value = data[key]
            tableName=year+key
            print(value)
            df=pd.DataFrame(columns=value[0],data=value[1:])
            df.to_sql(tableName, conn, if_exists='replace', index=False)
    df=pd.DataFrame(columns=data['taxCalculation'][0],data=data['taxCalculation'][1:])
    new_df=calculateSocialInsurance(df,data['socialSecurityAndHousingFund'])
    new_df.to_sql(year+'socialSecurityAndHousingFundAdjustment', conn, if_exists='replace', index=False)
    conn.close()
    result=[new_df.columns.tolist()]+new_df.values.tolist()
    return {'data':{'socialSecurityAndHousingFundAdjustment':result}}

        
def fetchAllTax(data):
    conn = sqlite3.connect(settings.PATH_DUCKDB+'/salay.db')
    year=data['filters']['fiscalYear']
    #遍历conn的所有表
    d={}
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    for table in tables:
        if table[0][:4]==year:
            query = f"SELECT * FROM [{table[0]}]" #数字开头的表需要引言
            value = cursor.execute(query).fetchall()
            title=[desc[0] for desc in cursor.description]
            data=[title]+value
            d[table[0][4:]]=data
    conn.close()
    return {'data':d}

def save_snapshot(data):
    conn = sqlite3.connect(settings.PATH_DUCKDB+'/salay.db')
    year=data['year']
    snapshot=str(data['snapshot'])
    conn.execute(f"CREATE TABLE IF NOT EXISTS workbook (year INTEGER PRIMARY KEY, data TEXT)")
    conn.execute(f"INSERT or REPLACE INTO workbook (year,data) VALUES (?,?)", (year,snapshot))
    conn.commit()
    conn.close()

def fetch_snapshot(data):
    conn = sqlite3.connect(settings.PATH_DUCKDB+'/salay.db')
    year=data['year']
    cursor = conn.cursor()
    cursor.execute("SELECT data FROM workbook WHERE year=?", (year,))
    result = eval(cursor.fetchall()[0][0])
    conn.close()
    return result
