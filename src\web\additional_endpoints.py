from fastapi import HTTPException
from typing import Dict, Any, List
import duckdb
from datetime import datetime
from src.web.quickly_query import QueryRequest, QueryResponse, build_where_clause, DB_PATH

# 内行查询API
async def query_internal_bank(request: QueryRequest):
    """查询内行查询"""
    try:
        conn = duckdb.connect(DB_PATH)

        where_clause = build_where_clause(request.filters, "内行查询")
        query = f"""
            SELECT * FROM 内行查询 
            WHERE {where_clause}
            LIMIT 1000
        """
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 内部对账API
async def query_internal_reconciliation(request: QueryRequest):
    """查询内部对账"""
    try:
        conn = duckdb.connect(DB_PATH)  
        where_clause = build_where_clause(request.filters, "内部对账")
        query = f"""
            SELECT * FROM 内部对账 
            WHERE {where_clause}
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 分供结算台账API
async def query_subcontractor_settlement(request: QueryRequest):
    """查询分供结算台账"""
    try:
        conn = duckdb.connect(DB_PATH)
        where_clause = build_where_clause(request.filters, "分供结算台账")
        query = f"""
            SELECT * FROM 分供结算台账 
            WHERE {where_clause}
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 外部确权台账API
async def query_external_confirmation(request: QueryRequest):
    """查询外部确权台账"""
    try:
        conn = duckdb.connect(DB_PATH)
        
        where_clause = build_where_clause(request.filters, "外部确权台账")
        query = f"""
            SELECT * FROM 外部确权台账 
            WHERE {where_clause}
            ORDER BY 过帐日期 DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 应付汇总按供应商API
async def query_payable_by_supplier(request: QueryRequest):
    """查询应付汇总按供应商"""
    try:
        conn = duckdb.connect(DB_PATH)
        where_clause = build_where_clause(request.filters, "应付汇总按供应商")
        query = f"""
            SELECT * FROM 应付汇总按供应商 
            WHERE {where_clause}
            ORDER BY 累计结算金额 DESC
            LIMIT 1000
        """
        
        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]
        
        data = [columns] + [list(row) for row in result]
        
        conn.close()
        
        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 成本表API
async def query_cost_ledger(request: QueryRequest):
    """查询成本表"""
    try:
        conn = duckdb.connect(DB_PATH)
        where_clause = build_where_clause(request.filters, "成本表")
        query = f"""
            SELECT * FROM 成本表
            WHERE {where_clause}
            ORDER BY 过帐日期 DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 收款台账API
async def query_receipt_ledger(request: QueryRequest):
    """查询收款台账"""
    try:
        conn = duckdb.connect(DB_PATH)

        # 创建表（如果不存在）
        
        where_clause = build_where_clause(request.filters, "收款台账")
        query = f"""
            SELECT * FROM 收款台账
            WHERE {where_clause}
            ORDER BY 过帐日期 DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

# 资金整理API
async def query_fund_management(request: QueryRequest):
    """查询资金整理"""
    try:
        conn = duckdb.connect(DB_PATH)

      
        where_clause = build_where_clause(request.filters, "资金整理")
        query = f"""
            SELECT * FROM 资金整理
            WHERE {where_clause}
            ORDER BY 过帐日期 DESC
            LIMIT 1000
        """

        result = conn.execute(query).fetchall()
        columns = [desc[0] for desc in conn.description]

        data = [columns] + [list(row) for row in result]

        conn.close()

        return QueryResponse(
            code=200,
            message="查询成功",
            data=data,
            timestamp=datetime.now().isoformat()
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")
