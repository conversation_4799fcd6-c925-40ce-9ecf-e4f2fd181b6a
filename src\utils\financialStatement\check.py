
from python_calamine import CalamineWorkbook
import src.base.cache as cache
import excelize
import re
import src.base.settings as settings

def completion_verification(check_path):
    f,error=excelize.open_file(check_path)
    fread=CalamineWorkbook.from_path(check_path).get_sheet_by_name("检查结果汇总").to_python()
    f.set_cell_value("检查结果汇总","a1",fread[0][4])
    f.save()
    f.close()

def read_formula(check_path):
    f,error=excelize.open_file(check_path)
    print(f.get_cell_formula("QY2008 外币结算金融资产及负债","b6"))
    print(f.get_cell_formula("QY2008 外币结算金融资产及负债","c59"))
    print(f.get_cell_formula("ZC2001 货币资金","d41"))
    f.close()

def replace_formula(check_path):
    f2,error=excelize.open_file(cache.CurrentYearFinancialReport)
    f3,error=excelize.open_file(cache.LastYearFinancialReport)
    f,error=excelize.open_file(check_path)
    for sheet_name in CalamineWorkbook.from_path(check_path).sheet_names:
        try:
            fread=CalamineWorkbook.from_path(check_path).get_sheet_by_name(sheet_name).to_python()
            for i in range(0,len(fread)):
                for j in range(0,len(fread[i])):
                    formula,error=f.get_cell_formula(sheet_name,excelize.coordinates_to_cell_name(j+1,i+1)[0])
                    if "[2]" in formula:
                        pattern = r"'\[2\].*'![A-Za-z]{1,2}\d+"
                        group_str = re.findall(pattern, formula)
                        for group in group_str:
                            query_sheetname = re.search(r"\].*'", group).group().replace("[","").replace("]","").replace("'","")
                            query_cellname = re.search(r"[A-Za-z]{1,2}\d+$", group).group()
                            query_value = f2.get_cell_value(query_sheetname,query_cellname)[0]
                            if query_value.replace(",","").strip().isnumeric():
                                formula = formula.replace(group, query_value.replace(",","").strip())
                            elif query_value!='':
                                formula = formula.replace(group, '"'+query_value+'"')
                            else:
                                formula = formula.replace(group, '0')
                        f.set_cell_formula(sheet_name,excelize.coordinates_to_cell_name(j+1,i+1)[0],formula)
                        
                    if "[1]" in formula:
                        pattern = r"'\[2\].*'![A-Za-z]{1,2}\d+"
                        group_str = re.findall(pattern, formula)
                        for group in group_str:
                            query_sheetname = re.search(r"\].*'", group).group().replace("[","").replace("]","").replace("'","")
                            query_cellname = re.search(r"[A-Za-z]{1,2}\d+$", group).group()
                            query_value = f3.get_cell_value(query_sheetname,query_cellname)[0]
                            if query_value.replace(",","").strip().isnumeric():
                                formula = formula.replace(group, query_value.replace(",","").strip())
                            elif query_value!='':
                                formula = formula.replace(group, '"'+query_value+'"')
                            else:
                                formula = formula.replace(group, '0')
                        f.set_cell_formula(sheet_name,excelize.coordinates_to_cell_name(j+1,i+1)[0],formula)
        except:
            pass
    new_path=check_path.replace(".xlsx","_new.xlsx")
    f.save_as(new_path)
    f.close()

import xlwings as xw
import re
import os

def find_and_replace_formula_links(excel_path,save_path=None):
    """
    查找并替换Excel中的公式链接
    
    参数:
    excel_path: Excel文件路径
    old_link_pattern: 要查找的旧链接模式(正则表达式)
    new_link: 要替换的新链接
    save_path: 保存路径，如果为None则覆盖原文件
    """
    # 打开Excel应用
    def wrap_filename_with_brackets(filepath):
        """
        Wraps the filename and its extension in square brackets.
        Example: 'example.xlsx' -> 'example[.xlsx]'
        """
        if not filepath:
            return filepath
        
        # Split into path and filename
        dirname = os.path.dirname(filepath)
        basename = os.path.basename(filepath)
        
        # Split into name and extension
        name, ext = os.path.splitext(basename)
        
        # Add brackets around extension if it exists
        if ext:
            new_basename = f"[{name}{ext}]"
        else:
            new_basename = name
        
        # Reconstruct the full path
        return os.path.join(dirname, new_basename)
    app = xw.App(visible=False)
    app.display_alerts = False
    app.screen_updating = False
    app.ask_to_update_links = False
    pathCurrentYearFinancialReport=cache.CurrentYearFinancialReport
    pathLastYearFinancialReport=cache.LastYearFinancialReport

    
    try:
        # 打开工作簿
        wb = app.books.open(excel_path)
        path=os.path.dirname(excel_path)
        # 遍历所有工作表
        for sheet in wb.sheets:
            print(sheet.name)
            # 获取已使用的范围
            used_range = sheet.used_range
            # 遍历每个单元格
            for row in used_range.rows:
                for cell in row:
                    # 检查单元格是否包含公式
                    if cell.formula.startswith('='):
                        formula = cell.formula
                        #print(formula)
                        formula = formula.replace(path+"\[成都中建一局建兴基础设施工程有限责任公司20241231.xlsx]",pathCurrentYearFinancialReport)
                        formula = formula.replace(path+"\[成都中建一局建兴基础设施工程有限责任公司20231231.xlsx]",pathLastYearFinancialReport)
                        cell.formula = formula
                        
                        '''# 使用正则表达式查找链接
                        matches = re.findall(old_link_pattern, formula)
                        
                        if matches:
                            # 存储替换信息
                            if sheet.name not in replacements:
                                replacements[sheet.name] = []
                            
                            old_formula = formula
                            
                            # 替换所有匹配的链接
                            formula = re.sub(old_link_pattern, new_link, formula)
                            
                            # 更新单元格公式
                            cell.formula = formula
                            
                            # 记录替换信息
                            replacements[sheet.name].append({
                                'cell': cell.address,
                                'old_formula': old_formula,
                                'new_formula': formula
                            })'''
        
        # 保存工作簿
        if save_path:
            print("已存储新版本")
            wb.save(save_path)
        else:
            print("未存储新版本")
            wb.save()      
        # 关闭工作簿
        wb.close()
    
    except Exception as e:
        print(f"发生错误: {e}")

    finally:
        # 关闭Excel应用
        app.quit()

import importlib.util
import sys
import os

def execute_function_from_pyc(pyc_path, function_name, *args, **kwargs):
    """
    从 pyc 文件中导入模块并执行指定函数

    参数:
        pyc_path (str): pyc 文件的路径
        function_name (str): 要执行的函数名称
        *args: 传递给函数的位置参数
        **kwargs: 传递给函数的关键字参数

    返回:
        function: 执行函数的结果
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(pyc_path):
            raise FileNotFoundError(f"找不到文件: {pyc_path}")

        # 获取模块名称（不包含扩展名）
        module_name = os.path.splitext(os.path.basename(pyc_path))[0]

        # 创建模块规范
        spec = importlib.util.spec_from_file_location(module_name, pyc_path)
        if spec is None:
            raise ValueError(f"无法为 {pyc_path} 创建模块规范")

        # 创建模块
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module

        # 执行模块（加载 pyc）
        spec.loader.exec_module(module)

        # 检查函数是否存在
        if not hasattr(module, function_name):
            raise AttributeError(f"模块 '{module_name}' 中没有名为 '{function_name}' 的函数")

        # 获取函数
        function = getattr(module, function_name)

        # 检查是否为可调用对象
        if not callable(function):
            raise TypeError(f"'{function_name}' 不是可调用对象")

        # 执行函数
        return function(*args, **kwargs)

    except Exception as e:
        print(f"执行过程中发生错误: {e}")
        raise
def main(inputFile,configWS,group):
    #print(cache.LastYearFinancialReport)
    #print(cache.CurrentYearFinancialReport)
    print("目前直接引用的安永版本")
    path=settings.CACHE_PATH+"/main.pyc"
    inputFile=inputFile
    configWS=configWS
    group=group
    save_path=inputFile.replace(".xlsx","_new.xlsx")
    find_and_replace_formula_links(inputFile,save_path)
    #replace_formula(inputFile)
    print("替换公式完成")
    print("开始审核")
    result = execute_function_from_pyc(path, 'data_op', save_path, configWS,group)
    #14 (('01-资产类', '02-负债类', '03-损益类', '04-税务类', '05-主表&权益类', '06-关联方&分部报告'))
