import sys
import os
from playwright.sync_api import Page
import time
import src.utils.cscec as cscec
from src.utils.DB.midIntrSQLiteDB import excelDB
from src.utils.DB.mainDB import mainDB
import pandas as pd
import src.utils.Browser.Browser as browser
import os
import re
import pdfplumber
import src.utils.fileui as fileui
from src.utils.ai.project_matcher import ProjectMatcher

def autoOne(page:Page,d: dict):  
    cscec.changeProjectCscec(page,d["组织机构"],d["项目名称"])              
    page.locator("//span[text()='税务系统']/parent::span/parent::li").click()
    page.locator("//span[contains(text(),'发票管理')]").click()
    page.locator("//span[contains(@class,'txt')][text()='发票收票登记单']").click() 
    page.locator("//span[text()='新建蓝票收票单']/parent::div").click()

    page.locator("//input[@id='FormTextInput5-input']").fill(d["事由"])
    s="//label[contains(text(),'合同编号')]/parent::div/following-sibling::div[1]/div/div/div"
    page.locator(s).first.click()
    page.get_by_placeholder("请输入查询合同编号关键字").fill(d["合同编号"].replace(";",""))
    page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
    page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//*[text()='"+d["合同编号"].replace(";","")+"']").first.dblclick()
    page.locator("//span[text()='影像']/parent::div/parent::div/parent::div/parent::div").click()
    page.locator("//span[text()='发票标签']/parent::li").click()
    with page.expect_file_chooser() as fc_info:
        page.locator("//img[contains(@src,'https://fip.cscec.com/OSPPortal/GWTStandard/images/scan/localimage.png')]").click()
        file_chooser = fc_info.value
        file_chooser.set_files(d["发票路径合并"].split(";"))
    page.locator("//*[text()='识别查验日志信息']/parent::div/parent::div/parent::div//div[text()='退出']").click(timeout=70000)
    page.locator("//*[text()='票据影像']/preceding-sibling::div[1]//td[1]").click() #点击关闭

    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
    time.sleep(0.5)
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div/parent::div//span[text()='提交']/parent::div/parent::div/parent::div/parent::div").click() #通过保存来确定提交按钮
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='提交']").click()
    page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
    page.locator("//div[@class='ant-tabs-tab ant-tabs-tab-with-remove ant-tabs-tab-active']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #关闭结算单
    page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click()



def main():
    db=excelDB()
    df=db.getDataframe("发票自动录入")
    page = browser.myBrowser("cscec").page
    for i,row in df.iterrows():
            if row["是否"]=="是":
                db.updateData("批量分包结算","是否","开始执行",i+1)
def queryData():
    db=excelDB()
    headers = ["序号","是否","组织机构","项目","事由","合同编号","发票路径合并","是否"]
    db.queryData("发票自动录入",headers)
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel("发票自动录入")


def extract_by_regex(text,data:list):
    pattern = r"名称：(.*?)\n"
    match = re.search(pattern, text)
    if match:
        result = match.group(1)
        data[1]=result
    pattern = r'统一社会信用代码/纳税人识别号：([A-Z0-9]{18})'
    match = re.search(pattern, text)
    if match:
        result = match.group(1)
        data[2]=result

def extract_from_path(path):
    data=[path,"","",""]
    with pdfplumber.open(path) as pdf:
        for page in pdf.pages:
            tables = page.extract_tables()
            for table in tables:
                for row in table:
                    #print(row)
                    for index,cell in enumerate(row):
                        if cell=='销\n售\n方\n信\n息':
                            extract_by_regex(row[index+1],data)
                        if cell=='备\n注':
                            data[3]=data[3]+row[index+1].strip().replace('\n','')
    return data

def getInvoiceData():
    projects=mainDB().conn.execute("SELECT DISTINCT 项目名称, 项目编号 FROM 一体化合同台账").fetchall()
    project_json=[{"id":row[1],"name":row[0]} for row in projects]
    directory_path = fileui.select_directory()
    row=["发票路径","名称","统一社会信用代码/纳税人识别号","备注","一体化项目名称","一体化合同名称"]
    data= [row]
    for filename in os.listdir(directory_path):
        if filename.endswith('.pdf'):
            # 构建文件的完整路径
            filename = os.path.join(directory_path, filename)
            row=extract_from_path(filename)
            row.append(calculateProject(row[3],project_json))
            row.append(calculateContract(row[1],row[4]))
            data.append(row)
    return data

def calculateProject(query,projects):
    matcher = ProjectMatcher(use_semantic=False) 
    matcher.load_project_list(projects)
    best_match = matcher.find_best_match(query, method="hybrid")
    if best_match:
        project_info, similarity = best_match
        return project_info['original']['name']
    else:
        return "未找到匹配项目"

def calculateContract(company,project):
    projects=mainDB().conn.execute("SELECT DISTINCT 合同名称 FROM 一体化合同台账 WHERE 项目名称=? AND 客商名称=?",(project,company)).fetchall()
    return [row[0] for row in projects]
    

