"""
基于RAG技术的项目名称模糊匹配功能
支持多种相似度计算方法，包括基础的字符串匹配和高级的语义匹配
"""

import re
import math
import difflib
from typing import List, Tuple, Dict, Optional, Union
import logging

# 尝试导入高级依赖，如果不存在则使用基础实现
try:
    import jieba
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False
    logging.warning("jieba not available, using basic tokenization")

try:
    from sentence_transformers import SentenceTransformer
    import numpy as np
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logging.warning("sentence-transformers not available, using basic similarity")

try:
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available, using basic similarity")


class ProjectMatcher:
    """项目名称模糊匹配器"""
    
    def __init__(self, use_semantic: bool = True, model_name: str = "all-MiniLM-L6-v2"):
        """
        初始化项目匹配器
        
        Args:
            use_semantic: 是否使用语义匹配（需要sentence-transformers）
            model_name: 语义匹配使用的模型名称
        """
        self.use_semantic = use_semantic and SENTENCE_TRANSFORMERS_AVAILABLE
        self.model = None
        self.tfidf_vectorizer = None
        self.project_list = []
        self.project_vectors = None
        
        if self.use_semantic:
            try:
                self.model = SentenceTransformer(model_name)
                logging.info(f"Loaded semantic model: {model_name}")
            except Exception as e:
                logging.warning(f"Failed to load semantic model: {e}, falling back to basic matching")
                self.use_semantic = False
        
        # 初始化TF-IDF向量化器（如果sklearn可用）
        if SKLEARN_AVAILABLE and not self.use_semantic:
            self.tfidf_vectorizer = TfidfVectorizer(
                analyzer='char',
                ngram_range=(1, 3),
                lowercase=True
            )
    
    def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        if not text:
            return ""
        
        # 移除多余空格
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 转换为小写
        text = text.lower()
        
        return text
    
    def _tokenize_chinese(self, text: str) -> List[str]:
        """中文分词"""
        if JIEBA_AVAILABLE:
            return list(jieba.cut(text))
        else:
            # 基础分词：按字符分割中文，按空格分割英文
            tokens = []
            current_word = ""
            
            for char in text:
                if '\u4e00' <= char <= '\u9fff':  # 中文字符
                    if current_word:
                        tokens.append(current_word)
                        current_word = ""
                    tokens.append(char)
                elif char.isalnum():
                    current_word += char
                else:
                    if current_word:
                        tokens.append(current_word)
                        current_word = ""
            
            if current_word:
                tokens.append(current_word)
            
            return tokens
    
    def _calculate_edit_distance(self, s1: str, s2: str) -> float:
        """计算编辑距离相似度"""
        if not s1 or not s2:
            return 0.0
        
        # 使用difflib计算相似度
        return difflib.SequenceMatcher(None, s1, s2).ratio()
    
    def _calculate_jaccard_similarity(self, s1: str, s2: str) -> float:
        """计算Jaccard相似度"""
        tokens1 = set(self._tokenize_chinese(s1))
        tokens2 = set(self._tokenize_chinese(s2))
        
        if not tokens1 and not tokens2:
            return 1.0
        
        intersection = tokens1.intersection(tokens2)
        union = tokens1.union(tokens2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _calculate_cosine_similarity_basic(self, s1: str, s2: str) -> float:
        """基础余弦相似度计算（基于字符频率）"""
        # 获取所有字符
        chars1 = list(s1)
        chars2 = list(s2)
        
        # 创建字符频率向量
        all_chars = set(chars1 + chars2)
        
        if not all_chars:
            return 1.0
        
        vector1 = [chars1.count(char) for char in all_chars]
        vector2 = [chars2.count(char) for char in all_chars]
        
        # 计算余弦相似度
        dot_product = sum(a * b for a, b in zip(vector1, vector2))
        magnitude1 = math.sqrt(sum(a * a for a in vector1))
        magnitude2 = math.sqrt(sum(b * b for b in vector2))
        
        if magnitude1 == 0 or magnitude2 == 0:
            return 0.0
        
        return dot_product / (magnitude1 * magnitude2)
    
    def _calculate_semantic_similarity(self, s1: str, s2: str) -> float:
        """计算语义相似度"""
        if not self.model:
            return 0.0
        
        try:
            embeddings = self.model.encode([s1, s2])
            similarity = np.dot(embeddings[0], embeddings[1]) / (
                np.linalg.norm(embeddings[0]) * np.linalg.norm(embeddings[1])
            )
            return float(similarity)
        except Exception as e:
            logging.warning(f"Semantic similarity calculation failed: {e}")
            return 0.0
    
    def calculate_similarity(self, query: str, target: str, method: str = "hybrid") -> float:
        """
        计算两个文本的相似度
        
        Args:
            query: 查询文本
            target: 目标文本
            method: 相似度计算方法 ("edit", "jaccard", "cosine", "semantic", "hybrid")
        
        Returns:
            相似度分数 (0-1)
        """
        query = self._preprocess_text(query)
        target = self._preprocess_text(target)
        
        if method == "edit":
            return self._calculate_edit_distance(query, target)
        elif method == "jaccard":
            return self._calculate_jaccard_similarity(query, target)
        elif method == "cosine":
            if SKLEARN_AVAILABLE:
                try:
                    vectors = self.tfidf_vectorizer.fit_transform([query, target])
                    similarity = cosine_similarity(vectors[0:1], vectors[1:2])[0][0]
                    return float(similarity)
                except:
                    pass
            return self._calculate_cosine_similarity_basic(query, target)
        elif method == "semantic":
            return self._calculate_semantic_similarity(query, target)
        elif method == "hybrid":
            # 混合方法：结合多种相似度计算
            scores = []
            
            # 编辑距离
            scores.append(self._calculate_edit_distance(query, target))
            
            # Jaccard相似度
            scores.append(self._calculate_jaccard_similarity(query, target))
            
            # 余弦相似度
            if SKLEARN_AVAILABLE:
                try:
                    vectors = self.tfidf_vectorizer.fit_transform([query, target])
                    cosine_sim = cosine_similarity(vectors[0:1], vectors[1:2])[0][0]
                    scores.append(float(cosine_sim))
                except:
                    scores.append(self._calculate_cosine_similarity_basic(query, target))
            else:
                scores.append(self._calculate_cosine_similarity_basic(query, target))
            
            # 语义相似度（如果可用）
            if self.use_semantic:
                semantic_score = self._calculate_semantic_similarity(query, target)
                scores.append(semantic_score)
                # 语义相似度权重更高
                return (sum(scores[:-1]) / len(scores[:-1])) * 0.4 + semantic_score * 0.6
            
            return sum(scores) / len(scores)
        else:
            raise ValueError(f"Unknown similarity method: {method}")

    def load_project_list(self, projects: List[Union[str, Dict]]) -> None:
        """
        加载项目列表

        Args:
            projects: 项目列表，可以是字符串列表或字典列表
                     如果是字典，应包含 'name' 或 'text' 字段
        """
        self.project_list = []

        for project in projects:
            if isinstance(project, str):
                self.project_list.append({
                    'text': project,
                    'original': project
                })
            elif isinstance(project, dict):
                # 尝试从字典中提取文本
                text = project.get('name') or project.get('text') or project.get('title') or str(project)
                self.project_list.append({
                    'text': text,
                    'original': project
                })
            else:
                # 其他类型转为字符串
                text = str(project)
                self.project_list.append({
                    'text': text,
                    'original': project
                })

        # 如果使用语义匹配，预计算向量
        if self.use_semantic and self.model:
            try:
                texts = [item['text'] for item in self.project_list]
                self.project_vectors = self.model.encode(texts)
                logging.info(f"Precomputed vectors for {len(texts)} projects")
            except Exception as e:
                logging.warning(f"Failed to precompute vectors: {e}")
                self.project_vectors = None

    def find_best_matches(self, query: str, top_k: int = 5,
                         method: str = "hybrid",
                         threshold: float = 0.0) -> List[Tuple[Dict, float]]:
        """
        查找最佳匹配的项目

        Args:
            query: 查询文本
            top_k: 返回前k个最佳匹配
            method: 相似度计算方法
            threshold: 相似度阈值，低于此值的结果将被过滤

        Returns:
            匹配结果列表，每个元素为 (项目信息, 相似度分数)
        """
        if not self.project_list:
            return []

        results = []

        # 如果使用语义匹配且有预计算向量
        if method == "semantic" and self.use_semantic and self.project_vectors is not None:
            try:
                query_vector = self.model.encode([query])
                similarities = np.dot(self.project_vectors, query_vector.T).flatten()

                for i, similarity in enumerate(similarities):
                    if similarity >= threshold:
                        results.append((self.project_list[i], float(similarity)))
            except Exception as e:
                logging.warning(f"Semantic search failed: {e}, falling back to basic search")
                # 回退到基础搜索
                method = "hybrid"

        # 基础搜索方法
        if not results:
            for project in self.project_list:
                similarity = self.calculate_similarity(query, project['text'], method)
                if similarity >= threshold:
                    results.append((project, similarity))

        # 按相似度排序并返回前k个
        results.sort(key=lambda x: x[1], reverse=True)
        return results[:top_k]

    def find_best_match(self, query: str, method: str = "hybrid",
                       threshold: float = 0.0) -> Optional[Tuple[Dict, float]]:
        """
        查找最佳匹配的项目

        Args:
            query: 查询文本
            method: 相似度计算方法
            threshold: 相似度阈值

        Returns:
            最佳匹配结果 (项目信息, 相似度分数) 或 None
        """
        matches = self.find_best_matches(query, top_k=1, method=method, threshold=threshold)
        return matches[0] if matches else None

    def batch_match(self, queries: List[str], method: str = "hybrid",
                   threshold: float = 0.0) -> List[Optional[Tuple[Dict, float]]]:
        """
        批量匹配查询

        Args:
            queries: 查询文本列表
            method: 相似度计算方法
            threshold: 相似度阈值

        Returns:
            匹配结果列表
        """
        return [self.find_best_match(query, method, threshold) for query in queries]


# 便捷函数
def create_project_matcher(projects: List[Union[str, Dict]],
                          use_semantic: bool = True) -> ProjectMatcher:
    """
    创建项目匹配器的便捷函数

    Args:
        projects: 项目列表
        use_semantic: 是否使用语义匹配

    Returns:
        配置好的项目匹配器
    """
    matcher = ProjectMatcher(use_semantic=use_semantic)
    matcher.load_project_list(projects)
    return matcher


def fuzzy_match_project(query: str, projects: List[Union[str, Dict]],
                       top_k: int = 5, method: str = "hybrid",
                       threshold: float = 0.0) -> List[Tuple[Dict, float]]:
    """
    项目模糊匹配的便捷函数

    Args:
        query: 查询文本
        projects: 项目列表
        top_k: 返回前k个最佳匹配
        method: 相似度计算方法
        threshold: 相似度阈值

    Returns:
        匹配结果列表
    """
    matcher = create_project_matcher(projects, use_semantic=(method == "semantic"))
    return matcher.find_best_matches(query, top_k, method, threshold)
