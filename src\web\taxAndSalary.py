from pydantic import BaseModel
from typing import Dict, Any, List
from datetime import datetime
import src.base.settings as settings
from fastapi import HTTPException
from src.web.http import app  
from fastapi import Request
import logging
import traceback

@app.post("/api/calculate-tax")
async def calculate_tax(request: Request):
    try:
        data = await request.json()
        from src.utils.taxAnaSalay.PersonalTax import calculateTax
        return calculateTax(data)
    except Exception as e:
        logging.error(f"Error in calculate-tax: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/push-declaration")
async def push_declaration(request: Request):
    try:
        data = await request.json()
        from src.utils.taxAnaSalay.salay import pushDeclaration
        pushDeclaration(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in push-declaration: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/build-finance-template")
async def build_finance_template(request: Request):
    try:
        data = await request.json()
        from src.utils.taxAnaSalay.salay import buildFinanceTemplate
        buildFinanceTemplate(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in build-finance-template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
        
@app.post("/api/salary-tax/save-all")
async def save_all_tax(request: Request):
    try:
        data = await request.json()
        from src.utils.taxAnaSalay.salay import saveAllTax
        saveAllTax(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in save-all-tax: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/salary-tax/fetch-all")
async def fetch_all_tax(request: Request):
    try:
        data = await request.json()
        from src.utils.taxAnaSalay.salay import fetchAllTax
        fetchAllTax(data)
        return {"code":200,"message":"success"}
    except Exception as e:
        logging.error(f"Error in fetch-all-tax: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
        
@app.post("/api/calculate-social-security-allocation")
async def calculate_social_security_allocation(request: Request):
    try:
        data = await request.json()
        from src.utils.taxAnaSalay.salay import calculate_social_security_allocation
        return calculate_social_security_allocation(data)
    except Exception as e:
        logging.error(f"Error in calculate-social-security-allocation: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/get-salay-snapsheet")
async def get_salay_snapsheet(request: Request):
    try:
        data = await request.json()
        from src.utils.taxAnaSalay.salay import fetch_snapshot
        return fetch_snapshot(data)
    except Exception as e:
        logging.error(f"Error in get-salay-snapsheet: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/save-salay-snapshot")
async def save_salay_snapshot(request: Request):
    try:
        data = await request.json()
        from src.utils.taxAnaSalay.salay import save_snapshot
        return save_snapshot(data)
    except Exception as e:
        logging.error(f"Error in save-salay-snapshot: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

