import openpyxl
from typing import List, Optional, Tuple
import src.utils.fileui as fileui

def find_header_row(worksheet, start_row: int = 4, end_row: int = 6) -> Optional[int]:
    """
    在指定范围内查找标题行

    Args:
        worksheet: openpyxl工作表对象
        start_row: 开始搜索的行号 (默认4)
        end_row: 结束搜索的行号 (默认6)

    Returns:
        标题行的行号，如果未找到返回None
    """
    for row_num in range(start_row, end_row + 1):
        # 检查该行是否有足够的非空单元格作为标题行
        non_empty_count = 0
        for col_num in range(1, worksheet.max_column + 1):
            cell_value = worksheet.cell(row=row_num, column=col_num).value
            if cell_value is not None and str(cell_value).strip():
                non_empty_count += 1

        # 如果该行有3个或更多非空单元格，认为是标题行
        if non_empty_count >= 3:
            return row_num

    return None


def get_header_mapping(worksheet, header_row: int) -> dict:
    """
    获取标题到列号的映射

    Args:
        worksheet: openpyxl工作表对象
        header_row: 标题行号

    Returns:
        标题名称到列号的字典映射
    """
    header_mapping = {}
    for col_num in range(1, worksheet.max_column + 1):
        cell_value = worksheet.cell(row=header_row, column=col_num).value
        if cell_value is not None:
            header_name = str(cell_value).strip()
            if header_name:
                header_mapping[header_name] = col_num

    return header_mapping


def write_list_to_excel(excel_file_path: str, data_list: List[List],
                       sheet_index: int = 0, start_row_range: Tuple[int, int] = (2, 6),newSheetName=None,savePath=None) -> bool:
    """
    将二维带标题的数据写入Excel文件

    Args:
        excel_file_path: Excel文件路径
        data_list: 二维列表，第一行为标题，后续行为数据
        sheet_index: 工作表索引，默认为0（第一个工作表）
        start_row_range: 标题行搜索范围，默认为(4, 6)
        newSheetName: 新工作表名称

    Returns:
        成功返回True，失败返回False
    """
    try:
        # 检查数据格式
        if not data_list or len(data_list) < 1:
            print("错误：数据列表为空或格式不正确")
            return False

        # 第一行是标题
        data_headers = data_list[0]
        data_rows = data_list[1:] if len(data_list) > 1 else []

        # 打开Excel文件
        workbook = openpyxl.load_workbook(excel_file_path)

        # 获取指定的工作表
        if sheet_index < len(workbook.worksheets):
            worksheet = workbook.worksheets[sheet_index]
        else:
            return False

        # 查找标题行
        header_row = find_header_row(worksheet, start_row_range[0], start_row_range[1])
        if header_row is None:
            print(f"错误：在第{start_row_range[0]}-{start_row_range[1]}行范围内未找到标题行")
            return False

        # 获取Excel中的标题映射
        excel_header_mapping = get_header_mapping(worksheet, header_row)

        # 创建数据标题到列索引的映射
        data_header_to_col = {}
        for i, header in enumerate(data_headers):
            if header in excel_header_mapping:
                data_header_to_col[i] = excel_header_mapping[header]
                print(f"匹配标题：'{header}' -> Excel列{excel_header_mapping[header]}")
            else:
                print(f"警告：数据标题 '{header}' 在Excel中不存在，将跳过")

        if not data_header_to_col:
            print("错误：没有找到匹配的标题")
            return False

        # 确定数据写入的起始行（标题行的下一行）
        data_start_row = header_row + 1

        # 写入数据
        for row_index, data_row in enumerate(data_rows):
            current_row = data_start_row + row_index

            # 确保数据行长度不超过标题长度
            for data_col_index, excel_col_num in data_header_to_col.items():
                if data_col_index < len(data_row):
                    value = data_row[data_col_index]
                    worksheet.cell(row=current_row, column=excel_col_num, value=value)

        # 保存文件
        if newSheetName is not None:
            if savePath is not None:
                newPath=savePath+"/"+newSheetName+".xlsx"
                workbook.save(newPath)
                print(f"成功写入 {len(data_rows)} 行数据到 {newPath}")
            else:
                path=fileui.select_directory("请选择"+newSheetName+"保存路径")
                newPath=path+"/"+newSheetName+".xlsx"
                workbook.save(newPath)
                print(f"成功写入 {len(data_rows)} 行数据到 {newPath}")
        else:
            workbook.save(excel_file_path)
            print(f"成功写入 {len(data_rows)} 行数据到 {excel_file_path}")
        return True

    except FileNotFoundError:
        print(f"错误：文件 {excel_file_path} 不存在")
        return False
    except Exception as e:
        print(f"错误：{str(e)}")
        return False


def demo_usage():
    """
    使用示例
    """
    # 示例数据 - 二维列表格式，第一行为标题
    sample_data = [
        ["姓名", "年龄", "部门", "工资", "奖金"],  # 标题行
        ["张三", 25, "技术部", 8000, 1000],      # 数据行1
        ["李四", 30, "销售部", 9000, 1500],      # 数据行2
        ["王五", 28, "技术部", 8500, 1200],      # 数据行3
    ]

    # 调用函数
    excel_path = "test_data.xlsx"
    success = write_list_to_excel(
        excel_file_path=excel_path,
        data_list=sample_data,
        sheet_index=0,
        start_row_range=(4, 6),
        newSheetName="test"
    )

    if success:
        print("数据写入成功！")
    else:
        print("数据写入失败！")


if __name__ == "__main__":
    demo_usage()
