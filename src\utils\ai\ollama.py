import ollama
import re
import time
import httpx
import os
import sys
import subprocess
from .project_matcher import ProjectMatcher, fuzzy_match_project

def check_and_set_env_variable(var_name, var_value):
    """检查环境变量是否存在，如果不存在则添加"""
    # 检查环境变量是否存在
    if var_name in os.environ:
        print(f"环境变量 {var_name} 已存在，值为: {os.environ[var_name]}")
        return True
    
    print(f"环境变量 {var_name} 不存在，正在设置...")
    
    try:
        # 使用setx命令设置环境变量
        # /M 参数表示设置为系统环境变量，去掉/M则设置为用户环境变量
        result = subprocess.run(
            ["setx", var_name, var_value, "/M"],
            capture_output=True,
            text=True,
            check=False
        )
        
        # 检查命令执行结果
        if result.returncode == 0:
            print(f"环境变量 {var_name} 已成功设置为: {var_value}")
            print("注意：新的环境变量可能需要重启终端或计算机才能生效")
            return True
        else:
            print(f"设置环境变量失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"执行过程中发生错误: {str(e)}")
        return False

def open_ollama():
    
    # 使用原始字符串来表示Windows路径，避免转义问题
    PATH_EXE = os.path.dirname(os.path.dirname(sys.executable))
    ollama_path = PATH_EXE + r"\ollama-windows-amd64\ollama.exe"
    check_and_set_env_variable("OLLAMA_MODELS", PATH_EXE + r"\ollama-windows-amd64")
    
    # 检查文件是否存在
    if not os.path.exists(ollama_path):
        print(f"错误: 找不到Ollama可执行文件在 {ollama_path}")
        return
    
    try:
        # 确保在Windows系统上运行
        if sys.platform == 'win32':
            # 使用CREATE_NEW_CONSOLE标志创建新的控制台窗口
            subprocess.Popen(
                [ollama_path, "serve"],
                creationflags=subprocess.CREATE_NEW_CONSOLE
            )
            print("Ollama服务已在新窗口中启动")
        else:
            print("此脚本仅适用于Windows系统")
    except Exception as e:
        print(f"启动Ollama服务时出错: {e}")

def get_ollama_response(prompt_text, model_name='qwen3:4b'):
    """
    使用同步方式调用Ollama模型，根据提示词生成内容。

    参数:
    - prompt_text (str): 发送给模型的提示。
    - model_name (str): 要使用的Ollama模型名称。默认为 'qwen:7b'。

    返回:
    - str: 模型生成的完整回复（已截取掉推理部分）。
    """
    #首先判断ollama是否开启
    try:
        # 尝试一个轻量级的API调用来检查服务是否可用
        ollama.list()
        print("Ollama服务已连接。")
    except:
        open_ollama()
        time.sleep(20)
        print("开启")

    try:
        response = ollama.chat(
            model=model_name,
            messages=[
                {
                    'role': 'user',
                    'content': prompt_text,
                },
            ]
        )
        content = response['message']['content']
        pattern = r'<think>.*?</think>'
        content = re.sub(pattern, '', content, flags=re.DOTALL)
        return content
            
    except Exception as e:
        print(f"调用Ollama时出错: {e}")
        return f"An error occurred: {e}"


def match_project_name(query: str, project_list: list, top_k: int = 5,
                      method: str = "hybrid", threshold: float = 0.1) -> list:
    """
    项目名称模糊匹配功能

    Args:
        query: 查询的项目名称
        project_list: 项目列表，可以是字符串列表或包含项目信息的字典列表
        top_k: 返回前k个最佳匹配，默认5个
        method: 匹配方法 ("edit", "jaccard", "cosine", "semantic", "hybrid")
        threshold: 相似度阈值，默认0.1

    Returns:
        匹配结果列表，每个元素包含项目信息和相似度分数

    Example:
        projects = ["中建三局项目", "中建二局工程", "中铁建设项目", "万科地产开发"]
        result = match_project_name("中建项目", projects, top_k=3)
        # 返回: [{'text': '中建三局项目', 'original': '中建三局项目', 'similarity': 0.85}, ...]
    """
    try:
        # 使用项目匹配器进行匹配
        matches = fuzzy_match_project(
            query=query,
            projects=project_list,
            top_k=top_k,
            method=method,
            threshold=threshold
        )

        # 格式化返回结果
        results = []
        for project_info, similarity in matches:
            result = {
                'text': project_info['text'],
                'original': project_info['original'],
                'similarity': round(similarity, 4)
            }
            results.append(result)

        return results

    except Exception as e:
        print(f"项目匹配时出错: {e}")
        return []


def get_project_suggestions_with_ai(query: str, project_list: list,
                                   model_name: str = 'qwen3:4b') -> dict:
    """
    结合AI和模糊匹配的项目建议功能

    Args:
        query: 查询文本
        project_list: 项目列表
        model_name: AI模型名称

    Returns:
        包含匹配结果和AI建议的字典
    """
    try:
        # 1. 先进行模糊匹配
        fuzzy_matches = match_project_name(query, project_list, top_k=3, method="hybrid")

        # 2. 构建AI提示词
        if fuzzy_matches:
            matched_projects = [match['text'] for match in fuzzy_matches]
            prompt = f"""
            用户查询: "{query}"

            基于模糊匹配找到的相关项目:
            {chr(10).join([f"- {proj}" for proj in matched_projects])}

            请分析用户查询的意图，并从匹配的项目中推荐最合适的项目。
            如果没有完全匹配的项目，请说明原因并给出建议。

            请用中文回答，格式简洁明了。
            """
        else:
            prompt = f"""
            用户查询: "{query}"

            在项目列表中没有找到相关的项目。
            项目列表包含: {len(project_list)} 个项目

            请分析可能的原因并给出建议，比如:
            1. 查询词是否过于模糊或具体
            2. 是否存在拼写错误
            3. 建议如何调整查询词

            请用中文回答。
            """

        # 3. 获取AI建议
        ai_suggestion = get_ollama_response(prompt, model_name)

        return {
            'query': query,
            'fuzzy_matches': fuzzy_matches,
            'ai_suggestion': ai_suggestion,
            'total_projects': len(project_list)
        }

    except Exception as e:
        return {
            'query': query,
            'fuzzy_matches': [],
            'ai_suggestion': f"处理过程中出现错误: {e}",
            'total_projects': len(project_list)
        }

if __name__ == '__main__':
    # --- 调用示例 ---
    print("--- 调用Ollama模型示例 ---")
    
    # 定义你的提示词
    my_prompt = "写一个财务报告，中建三局财务报告，收入为1000万，成本为800万，利润为200万，用于网页展示"
    
    print(f'正在向模型发送提示词: "{my_prompt}"')
    
    # 获取模型回复
    response_content = get_ollama_response(prompt_text=my_prompt)
    
    print("\n模型回复内容:")
    print(response_content)
