
#建立一个sqlite3数据库，用于中间交互
#该数据库用于存储中间数据，方便后续的查询和分析
#目前用于存储自动制证凭证数据，方便后期web端查询和分析

import src.base.settings as settings
import src.utils.fileui as fileui
import pandas as pd
import sqlite3
import excelize


#创建一个总的数据库类，存储所有中间数据
#数据写入方式为pandas的to_sql方法，数据读取方式为pandas的read_sql方法
#数据存储在sqlite3数据库中，方便后期查询和分析

def detect_long_integer_columns(df, sample_size=5, min_length=10):
    """
    检测可能是长整数类型的字符串列（如ID、编码等）
    这类列在Excel读取时可能被错误地转换为数值类型，导致精度丢失
    
    参数:
    - df: 输入的DataFrame
    - sample_size: 用于检测的样本行数
    - min_length: 认为是"长整数"的最小长度（字符数）
    
    返回:
    - 应被视为字符串的列名列表
    """
    # 获取数据的前sample_size行
    sample_df = df.head(sample_size)
    
    # 用于存储应被视为字符串的列名
    string_columns = []
    
    for col in df.columns:
        # 跳过非数值类型的列
        if not pd.api.types.is_numeric_dtype(df[col]):
            continue
            
        # 获取该列样本中的所有值
        sample_values = sample_df[col].dropna().tolist()
        
        # 如果样本中没有值，跳过该列
        if not sample_values:
            continue
            
        # 检查样本中的值是否都是整数（没有小数部分）
        all_integers = all(float(x).is_integer() for x in sample_values)
        
        # 如果所有值都是整数，检查转换为字符串后的长度
        if all_integers:
            # 将样本值转换为字符串（无小数部分）并检查长度
            str_lengths = [len(str(int(x))) for x in sample_values]
            # 如果有任何字符串长度大于等于min_length，将该列视为字符串列
            if any(length >= min_length for length in str_lengths):
                string_columns.append(col)
    
    return string_columns

def process_excel_long_integers(df:pd.DataFrame, sample_size=5, min_length=10):
    string_columns = detect_long_integer_columns(df, sample_size, min_length)
    for col in string_columns:
        df[col] = df[col].astype(str)

class webDB:
    def __init__(self):
        self.conn = sqlite3.connect(settings.PATH_DUCKDB+'/web.db')

    def writeData(self,tableName:str,data):
        #将data字典序列为
        data1=str(data[0])
        data2=str(data[1])
        self.conn.execute(f"CREATE TABLE IF NOT EXISTS {tableName} (id INTEGER PRIMARY KEY, data TEXT,timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,remark TEXT)")
        self.conn.execute(f"INSERT INTO {tableName} (data,remark) VALUES (?,?)", (data1,data2))
        self.conn.commit()
    def updateLastData(self,tableName:str,data):
        data1=str(data[0])
        data2=str(data[1])
        #maxId=self.conn.execute(f"SELECT MAX(rowid) FROM {tableName}").fetchall()[0][0]
        self.conn.execute(f"UPDATE {tableName} SET data = ? WHERE id = ?", (data2,data1))
        self.conn.commit()
    
    def deleteData(self,tableName:str,id:str):
        self.conn.execute(f"DELETE FROM {tableName} WHERE id = ?", (id,))
        self.conn.commit()

    def queryData(self,tableName:str,id:int|str):
        if id=='max':
            maxId=self.conn.execute(f"SELECT MAX(rowid) FROM {tableName}").fetchall()[0][0]
            data=self.conn.execute(f"SELECT * FROM {tableName} WHERE rowid = ?", (maxId,)).fetchall()[0][1]
            #字符串序列化为字典
            data=eval(data)
        else:
            data=self.conn.execute(f"SELECT * FROM {tableName} WHERE id = ?", (id,)).fetchall()[0][1]
            #字符串序列化为字典
            data=eval(data)
        return data
    def queryDatalist(self,tableName:str):
        data=self.conn.execute(f"SELECT id,timestamp,remark FROM {tableName}").fetchall()
        return data
    def close(self):
        self.conn.close()

class excelDB:
    def __init__(self):
        self.conn = sqlite3.connect(settings.PATH_DUCKDB+'/excel.db')
        self.createTable()

    def createTable(self):
        create='''
        CREATE TABLE IF NOT EXISTS excelpath(
        tableName TEXT  PRIMARY KEY,
        path TEXT
);'''
        self.conn.execute(create)

    def writeExcel(self,tableName:str|list):
        file_path = fileui.select_file()
        if file_path:
            if isinstance(tableName, str):
                df = pd.read_excel(file_path)
                process_excel_long_integers(df)
                df.to_sql(tableName, self.conn, if_exists='replace', index=False)
                self.conn.execute(f"INSERT OR REPLACE INTO excelpath (tableName,path) VALUES (?,?)", (tableName,file_path))
            else:
                for table in tableName:
                    df = pd.read_excel(file_path, sheet_name=table)
                    process_excel_long_integers(df)
                    df.to_sql(table, self.conn, if_exists='replace', index=False)
                    self.conn.execute(f"INSERT OR REPLACE INTO excelpath (tableName,path) VALUES (?,?)", (table,file_path))
            self.conn.commit()

    def queryData(self, tableName:str|list,columns:list=["未定义标题1"],data:list=["无数据"],fileName:str="查询结果"):
        if isinstance(tableName, str):
            try:
                df = pd.read_sql(f"SELECT * FROM {tableName}", self.conn)
            except:
                df = pd.DataFrame(columns=columns)
            directory = fileui.select_directory()
            df.to_excel(directory + f"/{tableName}.xlsx", index=False)
            print(f"数据已保存到{directory}/{tableName}.xlsx")
        else:
            directory = fileui.select_directory()
            with pd.ExcelWriter(directory+f"/{fileName}.xlsx") as writer:
                for i,table in enumerate(tableName):
                    try:
                        pd.read_sql(f"SELECT * FROM {table}", self.conn).to_excel(writer, sheet_name=table, index=False)
                    except:
                        df=pd.DataFrame(columns=columns[i],data=data[i])
                        df.to_excel(writer, sheet_name=table, index=False)
            print(f"数据已保存到{directory}/{fileName}.xlsx")
 
    def updateData(self, tableName:str, title:str, value:str, index:int):
        self.conn.execute(f"UPDATE {tableName} SET {title} = ? WHERE rowid = ?", (value, index))
        self.conn.commit()
        try:
            path=self.conn.execute(f"SELECT path FROM excelpath WHERE tableName = ?", (tableName,)).fetchall()[0][0]
        except:
            path=None
            pass
        titles0=self.conn.execute(f"PRAGMA table_info({tableName})").fetchall()
        titles=[t[1] for t in titles0]
        #print(titles)
        titleIndex=titles.index(title)
        if path:
            f,error=excelize.open_file(path)
            if error:
                pass
            else:
                f.set_cell_value(tableName,excelize.coordinates_to_cell_name(titleIndex+1,index+1)[0],value)
                f.save()
                f.close()
            
    def getDataframe(self, tableName:str):
        df = pd.read_sql(f"SELECT * FROM {tableName}", self.conn)
        return df
    
    def close(self):
        self.conn.close()


#自动制证凭证数据储存在excelDB中
class saveAutoCertification:
    def __init__(self):
        self.conn = sqlite3.connect(settings.PATH_DUCKDB+'/log.db')
        self.createTable()
        #创建表格，如果不存在的话

    def dropTable(self):
        self.conn.execute("DROP TABLE IF EXISTS 自动制证凭证")
        #清除缓存，防止占用空间
        self.conn.execute("VACUUM")

    def createTable(self):
        create='''
        CREATE TABLE IF NOT EXISTS 自动制证凭证(
        操作时间 TEXT,
        单据编号 TEXT,
        组织机构 TEXT,
        业务类型 TEXT,
        提单日期 TEXT,
        事由 TEXT,
        金额 TEXT,
        支付日期 TEXT,
        制证日期 TEXT,
        项目 TEXT,
        制证结果或临时凭证号码 TEXT
);'''
        self.conn.execute(create)
    
    def insertData(self, data):
        # 插入数据到表格中，data是一个列表，每个元素是一个元组，元组的元素是表格的
        self.conn.execute("INSERT INTO 自动制证凭证 VALUES (?,?,?,?,?,?,?,?,?,?,?)", data)
        self.conn.commit()

    def queryData(self, startDate, endDate):
        # 查询数据，返回一个列表，每个元素是一个元组，元组的元素是表格的每一行
        cursor = self.conn.execute("SELECT * FROM 自动制证凭证 WHERE 操作时间 BETWEEN ? AND ?", (startDate, endDate))
        data = cursor.fetchall()
        titles = [description[0] for description in cursor.description]
        data = [titles] + data
        return data
    
def queryAutoCertification(startDate, endDate):
    import tkinter as tk
    from tkinter import filedialog
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    # 设置窗口置顶
    root.attributes('-topmost', True)
    root.iconbitmap(settings.CACHE_PATH+"/rpa.ico")  # 设置窗口图标
    # 打开目录选择对话框
    selected_directory = filedialog.askdirectory(
        title="请选择一个目录",
        initialdir="/"  # 默认初始目录（可根据需要修改）
    )

    # 销毁隐藏的根窗口
    root.destroy()
    data=saveAutoCertification().queryData(startDate, endDate)
    import openpyxl
    wb=openpyxl.Workbook()
    ws=wb.active
    for row in data:
        ws.append(row)
    wb.save(selected_directory+'/自动制证凭证日志.xlsx')
    print('自动制证凭证日志已保存到'+selected_directory+'/自动制证凭证日志.xlsx')

def clearAutoCertification():
    # 清除自动制证凭证数据
    saveAutoCertification().dropTable()
    print('自动制证凭证数据已清除')