import flet as ft
from src.base import settings, register
from src.Gui.register import check_register_status
import base64
import asyncio
import src.Gui.callProcess as callF
from src.Gui.FletGui2.views.ui_constants import TEXT_COLOR, BG_COLOR, ACCENT_COLOR, ERROR_COLOR, SUCCESS_COLOR, SECONDARY_COLOR

class RegisterDialog(ft.UserControl):
    def __init__(self, on_register_success=None):
        super().__init__()
        self.on_register_success = on_register_success
        self.machine_code = register.gen_machine_sn()
        self.visible = True  # 添加可见性控制
        
    def close_dialog_click(self, e):
        print("关闭按钮被点击")
        self.visible = False  # 设置可见性为False
        self.update()  # 更新当前控件
        if self.on_register_success:
            self.on_register_success()
        
    def build(self):
        # 创建欢迎文本
        welcome_text = ft.Column(
            controls=[
                ft.Text(
                    "欢迎使用中建三局信息科技有限公司",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER,
                    color=TEXT_COLOR,
                ),
                ft.Text(
                    "财务机器人-信小财",
                    size=16,
                    weight=ft.FontWeight.BOLD,
                    text_align=ft.TextAlign.CENTER,
                    color=TEXT_COLOR,
                ),
            ],
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=5,
        )
        
        # 创建关闭按钮
        close_button = ft.IconButton(
            icon=ft.icons.CLOSE,
            icon_color=ft.colors.GREY_700,
            tooltip="关闭",
            on_click=self.close_dialog_click,
        )
        
        # 创建标题栏
        title_bar = ft.Row(
            controls=[
                ft.Container(expand=True),  # 左侧空白占位
                close_button,  # 右侧关闭按钮
            ],
            alignment=ft.MainAxisAlignment.END,
        )
        
        # 创建机器码显示框
        self.machine_code_field = ft.TextField(
            value=self.machine_code,
            read_only=True,
            label="本机机器码",
            expand=True,
            border_radius=10,
            border_color=ACCENT_COLOR,
            focused_border_color=ACCENT_COLOR,
            text_style=ft.TextStyle(size=14, color=TEXT_COLOR),
            min_lines=1,
            max_lines=1,
        )
        
        # 创建注册码输入框
        self.input_text = ft.TextField(
            value="",
            min_lines=2,
            max_lines=2,
            text_size=14,
            label="请输入专属注册码激活启用",
            expand=True,
            border_radius=10,
            border_color=ACCENT_COLOR,
            focused_border_color=ACCENT_COLOR,
        )
        
        # 创建注册按钮
        self.register_button = ft.ElevatedButton(
            "激活",
            icon=ft.icons.APP_REGISTRATION,
            on_click=self.handle_register,
            bgcolor=ACCENT_COLOR,
            color=ft.colors.WHITE,
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=10,
                text_style=ft.TextStyle(size=14, weight=ft.FontWeight.BOLD),
            ),
        )
        
        # 创建错误提示文本
        self.error_text = ft.Text(
            "",
            color=ERROR_COLOR,
            size=14,
            text_align=ft.TextAlign.CENTER,
        )
        
        # 创建激活失败提示文本
        self.fail_text = ft.Text(
            "",
            color=ERROR_COLOR,
            size=14,
            text_align=ft.TextAlign.CENTER,
        )
        
        # 创建激活成功提示文本
        self.success_text = ft.Text(
            "",
            color=SUCCESS_COLOR,
            size=14,
            text_align=ft.TextAlign.CENTER,
            weight=ft.FontWeight.BOLD,
        )
        
        # 创建半透明背景
        overlay = ft.Container(
            content=ft.Container(
                content=ft.Column(
                    controls=[
                        title_bar,  # 添加标题栏
                        welcome_text,
                        ft.Container(
                            content=self.machine_code_field,
                            padding=10,
                            border_radius=10,
                            bgcolor=SECONDARY_COLOR,
                            margin=ft.margin.only(bottom=10),
                        ),
                        ft.Container(
                            content=self.input_text,
                            padding=10,
                            border_radius=10,
                            bgcolor=SECONDARY_COLOR,
                            margin=ft.margin.only(bottom=10),
                        ),
                        ft.Container(
                            content=self.register_button,
                            padding=10,
                            border_radius=10,
                            margin=ft.margin.only(bottom=10),
                        ),
                        self.error_text,
                        self.fail_text,
                        self.success_text,
                    ],
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=10,
                    tight=True,  # 使列更紧凑
                ),
                padding=15,
                border_radius=20,
                bgcolor=BG_COLOR,
                width=400,
                height=400,  # 限制容器高度
                shadow=ft.BoxShadow(
                    spread_radius=1,
                    blur_radius=15,
                    color=ft.colors.with_opacity(0.3, ft.colors.BLACK),
                    offset=ft.Offset(0, 5),
                ),
            ),
            alignment=ft.alignment.center,
            bgcolor=ft.colors.with_opacity(0.5, ft.colors.BLACK),
            expand=True,
            visible=self.visible,  # 使用visible属性控制显示状态
        )
        
        return overlay
    
    async def close_dialog(self):
        await asyncio.sleep(1)  # 等待1秒
        self.page.dialog.open = False
        self.page.update()
        if self.on_register_success:
            self.on_register_success()
    
    async def handle_register(self, e):
        register_code = self.input_text.value.strip()
        if not register_code:
            self.error_text.value = "请输入注册码"
            self.fail_text.value = "激活失败"
            self.success_text.value = ""
            self.error_text.update()
            self.fail_text.update()
            self.success_text.update()
            return
            
        try:
            # 尝试解码注册码
            await asyncio.to_thread(base64.b32decode, register_code) #子线程等待
            ok, expire_time = await asyncio.to_thread(register.check_key_code, self.machine_code, register_code)
            if not ok:
                if not expire_time:
                    self.error_text.value = "激活码无效"
                else:
                    self.error_text.value = f"激活码已过期，过期时间：{expire_time}"
                self.fail_text.value = "激活失败"
                self.success_text.value = ""
                self.error_text.update()
                self.fail_text.update()
                self.success_text.update()
                return
                
            # 保存注册码
            register.write_key_code(settings.PATH_CONFIG, register_code)
            settings.REG.KEY_CODE = register_code
            settings.REG.EXPIRE_TIME = expire_time
            callF.regInstance.expire_time = expire_time #回传至这里更新状态
            
            # 显示激活成功
            self.error_text.value = ""
            self.fail_text.value = ""
            self.success_text.value = "激活成功"
            self.error_text.update()
            self.fail_text.update()
            self.success_text.update()
            
            # 显示成功消息
            self.page.show_snack_bar(
                ft.SnackBar(
                    content=ft.Text("激活成功"),
                    bgcolor=SUCCESS_COLOR,
                    duration=2000
                )
            )
            
            # 立即关闭弹窗
            self.page.dialog.open = False
            self.page.update()
            if self.on_register_success:
                self.on_register_success()
                
        except Exception as e:
            self.error_text.value = "注册码格式错误"
            self.fail_text.value = "激活失败"
            self.success_text.value = ""
            self.error_text.update()
            self.fail_text.update()
            self.success_text.update()
            return 