select
sum(case when (总账科目长文本 like '%可用存款%' or 总账科目长文本 like '银行存款%') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 原始存量,
sum(case when (总账科目长文本 like '%内部往来\内部借贷%' or 总账科目长文本 like '短期借款%') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 短期借款,
sum(case when (总账科目长文本 like '应付账款\应付供应链融资款') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 应付保理,
sum(case when (总账科目长文本 like '%内部往来\其他' or 总账科目长文本 like '%内部往来\代垫费用')  and (客户描述 in ('总部客商名称')) and 过帐日期 < $1 then 带符号的本位币金额 else 0 end) as 内部往来挂总部,
sum(case when (总账科目长文本 like '应付票据%') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 应付票据,
sum(case when (总账科目长文本 like '应收票据%') and 过帐日期 < $1  then 带符号的本位币金额 else 0 end) as 应收票据,
原始存量+应收票据+应付票据+短期借款+应付保理+内部往来挂总部 as 净存量,
sum(case when (总账科目长文本 like '%可用存款%' or 总账科目长文本 like '银行存款%') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 原始存量2,
sum(case when (总账科目长文本 like '%内部往来\内部借贷%' or 总账科目长文本 like '短期借款%') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 短期借款2,
sum(case when (总账科目长文本 like '应付账款\应付供应链融资款') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 应付保理2,
sum(case when (总账科目长文本 like '%内部往来\其他' or 总账科目长文本 like '%内部往来\代垫费用')  and (客户描述 in ('总部客商名称')) and 过帐日期 < $2 then 带符号的本位币金额 else 0 end) as 内部往来挂总部2,
sum(case when (总账科目长文本 like '应收票据%') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 应收票据2,
sum(case when (总账科目长文本 like '应付票据%') and 过帐日期 < $2  then 带符号的本位币金额 else 0 end) as 应付票据2,
原始存量2+应收票据2+应付票据2+短期借款2+应付保理2+内部往来挂总部2 as 净存量2
from 明细帐 