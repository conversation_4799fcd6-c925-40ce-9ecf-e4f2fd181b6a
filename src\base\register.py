"""
注册码校验
"""
import hashlib
import json
import base64
import os
import time

from Crypto.Cipher import AES
from Crypto.Util.Padding import pad, unpad
import wmi



_MAGIC_STR = 'R#f$UNm8OA'


class MyAesCbc:
    def __init__(self, key, iv):
        # key 和 iv 必须为 16 位
        self.key = key
        self.mode = AES.MODE_CBC
        self.cryptor = AES.new(self.key, self.mode, iv)

    def encrypt(self, plain_text):
        encode_text = plain_text.encode('utf-8')
        pad_text = pad(encode_text, AES.block_size)
        encrypted_text = self.cryptor.encrypt(pad_text)
        return encrypted_text

    def decrypt(self, encrypted_text):
        plain_text = self.cryptor.decrypt(encrypted_text)
        plain_text = unpad(plain_text, AES.block_size).decode()
        return plain_text


# aes 加密用到的 key 和 iv，长度必须为 16 位，随机字符串
Aes_key = 'K4B9P7M6P6G2W4R9'.encode()
Aes_IV = 'A7E2M7I4X3K0I0K7'.encode('utf-8')


# AES_CBC 加密
def encrypt(plain_text):
    e = MyAesCbc(Aes_key, Aes_IV).encrypt(plain_text)
    return e


# AES_CBC 解密
def decrypt(encrypted_text):
    d = MyAesCbc(Aes_key, Aes_IV).decrypt(encrypted_text)
    return d


def gen_machine_sn():
    """
    生成机器码
    """
    import pythoncom
    pythoncom.CoInitialize()
    m_wmi = wmi.WMI()
    cpu_info = m_wmi.Win32_Processor()
    parts = ['R#f$UNm8OA']
    if len(cpu_info) > 0:
        serial_number = cpu_info[0].ProcessorId
        parts.append(serial_number)
    board_info = m_wmi.Win32_BaseBoard()
    if len(board_info) > 0:
        board_id = board_info[0].SerialNumber.strip().strip('.')
        parts.append(board_id)

    parts.sort()
    sn = ''.join(parts).encode('utf-8')
    return hashlib.md5(sn).hexdigest().upper()


def check_key_code(machine_code, key_code) -> (bool, str):
    # 授权码解密，提取出激活码和有效期
    register_str = base64.b32decode(key_code)
    decode_key_data = json.loads(decrypt(register_str))
    active_code = decode_key_data["code"].upper()  # 激活码
    end_timestamp = decode_key_data["endTs"]  # 有效期

    # 加密机器码，用于跟激活码对比
    encrypt_code = encrypt(machine_code)
    md5_code = hashlib.md5(encrypt_code).hexdigest().upper()

    # 获取本地时间，用于跟有效期对比
    cur_ts = int(time.time())
    if md5_code != active_code:
        return False, ''
    time_local = time.localtime(end_timestamp)
    dt = time.strftime("%Y-%m-%d %H:%M:%S", time_local)
    if cur_ts >= end_timestamp:
        return False, dt
    else:
        return True, dt


def get_active_code(machine_code):
    """
    用于通过机器码，生成激活码
    machine_code: 机器码
    """
    encrypt_code = MyAesCbc(Aes_key, Aes_IV).encrypt(machine_code)
    active_code = hashlib.md5(encrypt_code).hexdigest().upper()
    return active_code


def get_time_limited_code(machine_code, timestamp):
    """
    用于通过机器码和有效期时间戳，生成限时授权码
    machine_code: 机器码
    timestamp: 有效期的时间戳
    """
    # 生成激活码
    active_code = get_active_code(machine_code)
    # 组合成 json 格式，并转换成字符串
    data = {
        "code": active_code,
        "endTs": timestamp,
    }
    text = json.dumps(data)
    # AES 加密
    encrypt_code = MyAesCbc(Aes_key, Aes_IV).encrypt(text)
    # base64 加密，生成授权码
    active_code = base64.b32encode(encrypt_code)
    return active_code.decode()


def read_key_code(path) -> str:
    home_dir = os.path.expanduser("~")
    license_file = os.path.join(path, 'license12.dat')
    if not os.path.exists(license_file):
        return ''
    with open(license_file, 'rb') as f:
        content = f.read()
        return content.decode('utf-8')


def write_key_code(path, key_code):
    os.makedirs(path, exist_ok=True)
    license_file = os.path.join(path, 'license12.dat')
    with open(license_file, 'wb') as f:
        f.write(key_code.encode('utf-8'))


def _test_expired():
    import src.base.settings as settings
    end_ts = int(time.time()) + 60*60*24*90
    sn = gen_machine_sn()
    write_key_code(settings.PATH_CONFIG, get_time_limited_code(sn, end_ts))


def tkinterPanel():
    import os
    import sys
    import tkinter as tk
    import tkinter.ttk as ttk
    script=os.path.abspath(__file__)  # 启动脚本.py的路径
    home=os.path.dirname(script)  # 工作目录
    os.chdir(home)
    sys.path.append("...")

    day=10
    end_ts = int(time.time()) + day*60*60*24
    root = tk.Tk()
    width=300
    height=400
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()

    x = (screen_width / 2) - (width / 2)
    y = (screen_height / 2) - (height / 2)

    def on_confirm():
        #sn = gen_machine_sn()
        sn="014B5FC8D2DABDEAD883AAA3A020D0F3"
        #print(get_time_limited_code(sn, end_ts))
        value1=input2.get()
        value2=int(time.time())+int(input1.get())*60*60*24
        value3=get_time_limited_code(value1, value2)
        input3.delete('1.0',tk.END)
        input3.insert(tk.END,value3)
    root.geometry(f'{width}x{height}+{int(x)}+{int(y)}')
    root.title("简易注册机")
    label = ttk.Label(root, text="输入天数")
    label.pack(pady=10)
    input1= ttk.Entry(root)
    input1.pack(pady=10)
    label = ttk.Label(root, text="输入机器码")
    label.pack(pady=10)
    input2= ttk.Entry(root,width=30)
    input2.pack(pady=10)
    confirm_button = ttk.Button(root, text="确定", command=on_confirm)
    confirm_button.pack(pady=10)
    label3 = ttk.Label(root, text="下方授权码")
    label3.pack(pady=10)
    input3= tk.Text(root, height=10, width=40)
    input3.pack(pady=30)
    root.mainloop()

    


