
import src.utils.Browser.Browser as myBrowser
import src.utils.cscec as cscec
import time
import src.base.settings as settings
import pandas as pd
import os
from src.utils.DB.configDB import configDB
from src.utils.Browser.Browser import myBrowser
import src.utils.dop as dop
from datetime import datetime
import src.utils.DB.mainDB as mainDB
import src.utils.fileui as fileui

def get_month_based_on_date() -> str:
    """
    根据当前日期判断返回月份
    如果当前日期在25号之后(不包括25号)，则返回下个月，否则返回当月
    返回格式为：xxxx年xx月
    """
    today = datetime.now()
    
    # 如果当前日期大于25号
    if today.day > 25:
        # 获取下个月的第一天
        if today.month == 12:
            next_month = today.replace(year=today.year + 1, month=1, day=1)
        else:
            next_month = today.replace(month=today.month + 1, day=1)
        month_str = next_month.strftime('%Y年%m月')
    else:
        month_str = today.strftime('%Y年%m月')
    
    return month_str

def main():
    dopUser=configDB().fqUserSettings
    B=myBrowser("3b",dopUser["username"],dopUser["password"])
    page=B.page
    print("启动浏览器成功")
    dop.goFund(page,True)
    dop.goFundContract(page)
    page.locator("//i[@class='icon aufontAll  h-icon-all-close']").click()
    time.sleep(2)
    page.locator("//i[@class='icon aufontAll h-icon-all-filter-o']").click()
    page.locator("//div[@title='期间']/parent::div/following-sibling::div[1]//input").fill(get_month_based_on_date())
    page.locator("//span[text()='查 询']/parent::button").click()
    with page.expect_download(timeout=60000) as download_info:
        page.locator("//span[text()='自定义导出']/parent::button").click()
        download=download_info.value
        download.save_as(settings.PATH_DOWNLOAD+"/财商债务台账原始数据.xlsx")
    df=pd.read_excel(settings.PATH_DOWNLOAD+"/财商债务台账原始数据.xlsx",header=1,dtype={'司库项目编码': str,'分供方合同编码': str})
    os.remove(settings.PATH_DOWNLOAD+"/财商债务台账原始数据.xlsx")
    compare(df)
    
def compare(df):
    df1=mainDB.mainDB().conn.execute('''
    select
    一体化合同台账.*,
    df."其中，价税合计" as 财商结算金额,
    df."按合同应付金额（含税）" as 财商按合同应付金额,
    df."累计已付款（含预付款）" as 财商已付款,
    结算金额-财商结算金额 as 结算差额,
    已付金额-财商已付款 as 已付款差额
    from 一体化合同台账 left join df on 一体化合同台账.项目编号=df.司库项目编码 and 一体化合同台账.合同编号=df.分供方合同编码 where (合同类型 not like '%承包%' and 合同类型 not like '%协议书%' and 税率 like '进项%') ''').df()
    path=fileui.select_directory()
    df1.to_excel(path+"/财商债务台账比对.xlsx")
    print("文件保存在："+path+"/财商债务台账比对.xlsx")



