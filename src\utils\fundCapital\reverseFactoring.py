import pandas as pd
import datetime
from playwright.sync_api import sync_playwright
import src.utils.cscec as cscec
import src.utils.fileui as fileui
from src.utils.DB.midIntrSQLiteDB import excelDB
from src.utils.fundCapital.filechoose import TemplateGeneratorApp
import os
import time
import src.utils.Browser.Browser as B<PERSON>er


def query(queryValue,queryList):
    for i in range(len(queryList)):
        if queryList[i] in queryValue:
            return i

def main():
    #readPath=fileui.select_file()
    db=excelDB()
    df=db.getDataframe("反向保理申请")
    queryList=df["一体化申请单号"].tolist()
    page=Browser.myBrowser("cscec").page
    cscec.toFunction(page,"司库系统","资金结算","待支付列表")
    cscec.getVisible(page,"//div[text()='供应链资产证券化']/parent::div/parent::div/preceding-sibling::div//div[text()='反向保理申请']").click()
    startFDT=datetime.datetime.now()-datetime.timedelta(days=179)
    #重设开始日期为180天
    cscec.changeDate(page,startFDT.strftime("%Y-%m-%d"),"startFDT-input")
    time.sleep(0.3)
    cscec.getVisible(page,"//span[text()='查询']").click()
    cscec.getVisible(page,"//table//div[(text()='1')]")
    table=cscec.cscecTable(page,"申请单号")
    table.reIndex()
    count=table.count
    for i in range(count,0,-1):
        print(table.getValue(i,"申请单号"))
        moduleIndex=query(table.getValue(i,"申请单号"),queryList)
        if moduleIndex is not None and moduleIndex>-1:
            row=df.loc[moduleIndex]
            table.click(i,"状态")
            page.locator("//img/following-sibling::span[text()='反向保理申请']").click()
            cscec.fillLalbel_input(page,"*合同编号：",row["合同编号"])
            cscec.fillLalbel_input(page,"*合同名称：",row["合同名称"])
            cscec.Lablechoose(page,"核心企业：","请输入查询关键字","帮助字典",row["核心企业"])
            #提取公司之前的字符
            cscec.Lablechoose(page,"资金方：","请输入查询关键字","帮助字典",row["资金方"].split("公司")[0]+"公司")
            cscec.fillLalbel_input(page,"资金方隶属集团",row["资金方"].split("公司")[0]+"公司")
            days=(datetime.datetime.strptime(row["账期到期日期"],"%Y-%m-%d")-datetime.datetime.strptime(row["账期起始日期"],"%Y-%m-%d")).days
            cscec.changeDate(page,row["账期起始日期"],"FormDateField4-input")
            cscec.fillLalbel_input(page,"合同期限",str(days+1))
            cscec.fillLalbel_input(page,"保理合同成本：",str(row["融资成本(元)"]))
            cscec.fillLalbel_input(page,"供应商承担保理合同成本金额：",str(row["融资成本(元)"]))
            cscec.fillLalbel_input(page,"保理合同利率：",str(row["保理合同利率"]))
            cscec.fillLalbel_input(page,"批准文号：",str(row["支付单号"]))
            cscec.fillLalbel_input(page,"*资金提供机构：",row["资金方"].split("公司")[0]+"公司")
            cscec.fillLalbel_input(page,"*资金提供机构：",row["资金方"].split("公司")[0]+"公司")
            page.locator("//label",has_text='我方增信方式：').locator("//parent::div/following-sibling::div[1]//input").click()
            if row["我方增信方式"]=="其他":
                page.locator("//div[text()='确权']/following-sibling::div[1]").click()
            elif row["我方增信方式"]=="无":
                page.locator("//div[text()='确权']/following-sibling::div[2]").click()
            else:
                page.locator("//div[text()='确权']").click()
            cscec.getVisible(page,"//img[contains(@src,'增加')]").click()
            table2=cscec.cscecTable(page,"授信单位")
            table2.reIndex()
            table2.clickInputQuery(1,"授信单位")
            cscec.dialogInput(page,row["授信单位"])
            table2.clickInputQuery(1,"授信合同")
            cscec.dialogInput(page,row["授信合同"])
            table2.fillInput(1,"本次占用额度",str(row["支付金额(元)"]))
            #cscec.fillLalbel_input(page,"实际融资金额：",str(row["支付金额(元)"])) 不允许编辑
            cscec.fillLalbel_input(page,"*备 注：",row["备注"])
            cscec.fillLalbel_input(page,"承担成本路径：","结算")
            if False:
                page.locator("//label[text()='是否框架合同：']/parent::div/following-sibling::div[1]//label").click()
            path=row["附件"]
            if path and os.path.exists(path):
                cscec.uploadAttachment(page,path)
            cscec.getVisible(page,"//span[text()='保存']").click()
            cscec.clickDigalog(page,"提示")
            cscec.clickDigalog(page,"提示")
            time.sleep(1)
            cscec.getVisible(page,"//span[contains(text(),'提交')]/parent::div/parent::div").click()
            cscec.clickDigalog(page,"处理意见")
            cscec.clickDigalog(page,"提示")
            cscec.closeTab(page)

def queryData():
    app=TemplateGeneratorApp()
    app.mainloop()
    print("生成成功")


def writeData():
    db=excelDB()
    db.writeExcel("反向保理申请")