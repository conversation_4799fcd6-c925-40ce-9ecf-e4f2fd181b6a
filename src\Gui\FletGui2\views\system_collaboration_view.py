# 系统协同
import flet as ft
from src.Gui.FletGui2.views import ui_constants as C
from src.Gui.FletGui2.components.button import create_execute_button_with_row,create_execute_button_with_text_row1
from src.Gui.FletGui2.components.button import create_execute_button_with_row4
from src.Gui.FletGui2.components.date_components import creatDate_panel
import src.base.settings as settings

def create_tab1(page: ft.Page):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[
                    creatDate_panel(page,"初始化收支台账",settings.LAST_MONTH_FIRST_DAY,settings.LAST_MONTH_LAST_DAY,"初始化收支台账"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_text_row1(page,"填入收支台账","填入收支台账","上传收支台账")
                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )
def create_tab2(page: ft.Page):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[
                    create_execute_button_with_text_row1(page, "初始化财商计划模版", "根据合同台账生成计划模版", "初始化财商计划模版"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_row(page,"财商计划推送","更新财商计划模版","查询财商计划推送","财商计划推送")

                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )
def create_tab3(page: ft.Page):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[
                    create_execute_button_with_row(page,"特殊支付","获取特殊支付模版","更新特殊支付模版","批量特殊支付"),


                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )
def create_tab4(page: ft.Page):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[create_execute_button_with_row4(page,"商务推送","打开chrome浏览器","查询商务推送模版","更新商务推送模版","批量商务推送"),

                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )

def create_tab5(page: ft.Page):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[create_execute_button_with_text_row1(page,"财商债务台账比对","先到统计大师哪里同步一体化合同台账","获取财商数据差额"),

                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )
def get_view(page: ft.Page, view_display_name: str, show_home_callback):


    def _handle_return_click(e):
        show_home_callback()

    # Header section
    header = ft.Container(
        content=ft.Row(
            [
                ft.Text(
                    f"{view_display_name}模块",
                    size=24, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON

                ),
                ft.Container(),  # 占位符用于左右分隔
                ft.ElevatedButton(
                    "返回主菜单",
                    icon=ft.icons.ARROW_BACK_IOS_NEW_ROUNDED,
                    on_click=_handle_return_click,
                    style=ft.ButtonStyle(
                        color=ft.colors.WHITE,
                        bgcolor=C.ACCENT_COLOR,
                        padding=ft.padding.symmetric(horizontal=24, vertical=12),
                        shape=ft.RoundedRectangleBorder(radius=8),
                        elevation=3,
                        shadow_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
                    ),
                    height=48
                ),
            ],
            spacing=8,
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            vertical_alignment=ft.CrossAxisAlignment.CENTER
        ),
        padding=ft.padding.only(bottom=20),
        alignment=ft.alignment.center
    )

    # Create tab content for each test case
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="财商收支台账",
                icon=ft.icons.PAYMENT,
                content=create_tab1(page)
            ),
                        ft.Tab(
                text="财商资金计划",
                icon=ft.icons.COMPUTER_OUTLINED,
                content=create_tab2(page)
            ),
                        ft.Tab(
                text="财商特殊支付",
                icon=ft.icons.COMPUTER_OUTLINED,
                content=create_tab3(page)
            ),
                        ft.Tab(
                text="财商商务推送",
                icon=ft.icons.COMPUTER_OUTLINED,
                content=create_tab4(page)
            ),
            ft.Tab(
                text="财商数据比对",
                icon=ft.icons.COMPUTER_OUTLINED,
                content=create_tab5(page)
            )
        ],
        expand=True,
        tab_alignment=ft.TabAlignment.CENTER,
        label_color=C.ACCENT_COLOR,
        unselected_label_color=ft.colors.with_opacity(0.7, C.TEXT_COLOR),
        indicator_color=C.ACCENT_COLOR,
        indicator_tab_size=True,
        label_padding=ft.padding.symmetric(horizontal=20, vertical=12),
        # Enhanced tab styling
        overlay_color=ft.colors.with_opacity(0.1, C.ACCENT_COLOR),
        divider_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
        indicator_border_radius=4,
        indicator_padding=ft.padding.symmetric(horizontal=8),
    )

    # Create tabs with improved styling
    
    scrollable_content = ft.Container(
        content=ft.Column(
            [
                header,
                # Tabs container with proper expansion
                ft.Container(
                    content=tabs,
                    expand=True,
                    margin=ft.margin.symmetric(vertical=10),
                )
            ],
            spacing=0,
            expand=True,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
        ),
        expand=True,
        padding=ft.padding.symmetric(horizontal=15, vertical=10)
    )
    
    # Main container with proper constraints
    return ft.Container(
        content=scrollable_content,
        expand=True,
        padding=ft.padding.only(top=10, bottom=10),
        margin=ft.margin.all(0)
    )
