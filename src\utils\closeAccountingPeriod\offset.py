from python_calamine import CalamineWorkbook
import pandas as pd
import duckdb
import src.utils.fileui as fileui
from src.utils.DB.midIntrSQLiteDB import excelDB
from src.utils.DB.mainDB import mainDB
def reportOffset():
    path=fileui.select_file()
    title=["合并规则","本方单位","对方单位","科目","借方金额","贷方金额"]
    result=[]
    result.append(title)
    workbook = CalamineWorkbook.from_object(path)
    wsNames=["本级-已抵销","本级-未抵销"]
    def returnList(wsName):
        theList = workbook.get_sheet_by_name(wsName).to_python()
        titleList=[elem.strip() for elem in theList[0]]
        titleIndex=[titleList.index(ele) for ele in title]
        for row in theList[1:]:
            row2=[0 if row[ele]=="" else row[ele] for ele in titleIndex]
            result.append(row2)
    for wsName in wsNames:
        returnList(wsName)
    df=pd.DataFrame(result[1:],columns=result[0])
    a=duckdb.sql(
'''
with a as (
select any_value(本方单位) as 本方单位,
any_value(对方单位) as 对方单位, 
sum(case when (科目 like '%应付账款-税金%') then 借方金额+贷方金额 else 0 end) as 应付账款税金,
sum(case when (科目 like '%应付账款-其他%') then 借方金额+贷方金额 else 0 end) as 应付账款其他,
sum(case when (科目 like '%一年内到期的非流动负债-应付账款%') then 借方金额+贷方金额 else 0 end) as 一年内到期的非流动负债应付账款,
sum(case when (科目 like '%长期应付款-应付账款%') then 借方金额+贷方金额 else 0 end) as 长期应付款应付账款,
sum(case when (科目 like '%其他应付款-保证金%') then 借方金额+贷方金额 else 0 end) as 其他应付款保证金,
sum(case when (科目 like '%应付票据%') then 借方金额+贷方金额 else 0 end) as 应付票据,
sum(case when (科目 like '%存货-合同履约成本%') then 借方金额+贷方金额 else 0 end) as 存货合同履约成本,
sum(case when (科目 like '%主营业务成本-%') then 借方金额+贷方金额 else 0 end) as 主营业务成本,
sum(case when (科目 like '分包工程支出%' or 科目 like '支付%' or 科目 like '收到%') then 借方金额+贷方金额 else 0 end) as 现金流出
from df group by 本方单位,对方单位),
b as (
select any_value(本方单位) as 本方单位,
any_value(对方单位) as 对方单位, 
sum(case when (科目 like '%应收账款-其他%') then 借方金额+贷方金额 else 0 end) as 应收账款其他,
sum(case when (科目 like '%其他非流动资产-质保金-原值%') then 借方金额+贷方金额 else 0 end) as 其他非流动资产质保金原值,
sum(case when (科目 like '%一年内到期的非流动资产-质保金-原值%') then 借方金额+贷方金额 else 0 end) as 一年内到期的非流动资产质保金原值,
sum(case when (科目 like '%合同资产-已完工尚未结算款%') then 借方金额+贷方金额 else 0 end) as 合同资产已完工尚未结算款,
sum(case when (科目 like '%合同资产-其他%') then 借方金额+贷方金额 else 0 end) as 合同资产其他,
sum(case when (科目 like '%应收账款-待转销项税额%') then 借方金额+贷方金额 else 0 end) as 应收账款待转销项税额,
sum(case when (科目 like '%合同负债%') then 借方金额+贷方金额 else 0 end) as 合同负债,
sum(case when (科目 like '%主营业务收入-%') then 借方金额+贷方金额 else 0 end) as 主营业务收入,
sum(case when (科目 like '收到%' or 科目 like '支付%' or 科目 like '构建%') then 借方金额+贷方金额 else 0 end) as 现金流入
from df group by 本方单位,对方单位)
select 
b.*,
a.*,
round(应付账款税金+应付账款其他+一年内到期的非流动负债应付账款+长期应付款应付账款+其他应付款保证金+应付票据,2) as 应付合计   ,
round(应收账款其他+其他非流动资产质保金原值+一年内到期的非流动资产质保金原值+合同资产已完工尚未结算款+合同资产其他+应收账款待转销项税额+合同负债,2) as 应收合计,
round(应付合计-应收合计,2) as 应收应付抵消差额,
round(主营业务成本-存货合同履约成本-主营业务收入,2) as 收入成本抵消差额,
round(现金流入-现金流出,2) as 现金流入流出抵消差额
from a full join b on a.本方单位=b.对方单位 and a.对方单位=b.本方单位'''
).df()
    #a.to_excel(r"c:\Users\<USER>\Desktop\结果.xlsx", index=False)
    n1=[a.columns.values.tolist()]+a.fillna(0).values.tolist()
    return n1

#main(r"c:\Users\<USER>\Desktop\1030851中建三局智能技术有限公司调整抵销分录数据.xlsx")

def bookOffsetSituation():
    df1=excelDB().getDataframe("收入成本测算")
    df2=duckdb.execute('''select any_value(项目名称) AS 项目名称,any_value(利润中心编码) AS 利润中心,sum("结账后已完工未结算（负数为合同负债）") as 结账后已完工未结算,sum(累计收款) as 累计收款,sum(累计含税确权) as 累计确权 from df1 GROUP BY 利润中心编码''').df()
    df3=mainDB().conn.execute("select sum(结算额) as 总包结算额,sum(付款额) as 总包付款额,sum(暂估额) as 总包暂估额,any_value(利润中心) AS 利润中心 from 内部对账 GROUP by 利润中心").df()
    df4=duckdb.execute('''
    select df2.项目名称 as 项目名称,df2.利润中心 as 利润中心,df2.结账后已完工未结算 as 结算后已完工未结算,df2.累计收款 as 累计收款,df2.累计确权 as 累计确权,df3.总包结算额 as 总包结算额,df3.总包付款额 as 总包付款额,df3.总包暂估额 as 总包暂估额,
    df2.结账后已完工未结算-df3.总包暂估额 as 暂估差额,
    df2.累计确权-df3.总包结算额 as 结算差额,
    df2.累计收款-df3.总包付款额 as 付款差额
    from df2 left join df3 on df2.利润中心=df3.利润中心
    ''').df()
    return [df4.columns.values.tolist()]+df4.fillna(0).values.tolist()
    



    



