import sys
sys.path.append(".")

import src.utils.sapPublic.GetSAPSession as GetSAPSession
import pandas as pd
import openpyxl
import time

def getTxt():
    session = GetSAPSession.creatSAP()
    session.startTransaction("ZARAP0012")
    session.findById("wnd[0]/usr/ctxtS_BUKRS-LOW").text = "3062"
    session.findById("wnd[0]/usr/ctxtP_KHINR").text = "G300000047"
    session.findById("wnd[0]/usr/txtP_GJAHR").text = "2025"
    session.findById("wnd[0]/usr/txtP_MONAT").text = "6"
    session.findById("wnd[0]/usr/txtP_MONAT").setFocus()
    session.findById("wnd[0]/usr/txtP_MONAT").caretPosition = 2
    session.findById("wnd[0]/usr/btn%_S_SAKNR_%_APP_%-VALU_PUSH").press()
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV").select()
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV/ssubSCREEN_HEADER:SAPLALDB:3030/tblSAPLALDBSINGLE_E/ctxtRSCSEL_255-SLOW_E[1,0]").text = "1221080000"
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV/ssubSCREEN_HEADER:SAPLALDB:3030/tblSAPLALDBSINGLE_E/ctxtRSCSEL_255-SLOW_E[1,1]").text = "1221060000"
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV/ssubSCREEN_HEADER:SAPLALDB:3030/tblSAPLALDBSINGLE_E/ctxtRSCSEL_255-SLOW_E[1,1]").setFocus()
    session.findById("wnd[1]/usr/tabsTAB_STRIP/tabpNOSV/ssubSCREEN_HEADER:SAPLALDB:3030/tblSAPLALDBSINGLE_E/ctxtRSCSEL_255-SLOW_E[1,1]").caretPosition = 0
    session.findById("wnd[1]/tbar[0]/btn[8]").press()
    session.findById("wnd[0]/tbar[1]/btn[8]").press()
    session.findById("wnd[0]/mbar/menu[0]/menu[1]/menu[0]").select()
    session.findById("wnd[0]/mbar/menu[0]/menu[1]/menu[2]").select()
    session.findById("wnd[1]/tbar[0]/btn[0]").press()
    session.findById("wnd[1]/usr/ctxtDY_PATH").setFocus()
    session.findById("wnd[1]/usr/ctxtDY_PATH").caretPosition = 0
    session.findById("wnd[1]").sendVKey(4)
    session.findById("wnd[2]/usr/ctxtDY_PATH").text = "C:\\Users\\<USER>\\Desktop"
    session.findById("wnd[2]/usr/ctxtDY_FILENAME").text = "坏账测试.txt"
    session.findById("wnd[2]/usr/ctxtDY_FILENAME").caretPosition = 7
    session.findById("wnd[2]/tbar[0]/btn[0]").press()
    session.findById("wnd[1]/tbar[0]/btn[0]").press()
    session.findById("wnd[0]").close()
    session.findById("wnd[1]/usr/btnSPOP-OPTION1").press()

def ReadTextFileToArray(path):
    # 计算以"|"开头的行数k
    k = 0
    file_lines = []
    with open(path, "r") as file:
        for line in file:
            file_lines.append(line.strip())
    for line in file_lines:
        if line.startswith("|"):
            k += 1
    
    # 根据第4行数据确定列数
    line_data = file_lines[3].split("|")
    columns = len(line_data)
    
    # 创建二维列表，初始化为空字符串
    data_array = [["" for _ in range(columns)] for _ in range(k)]
    
    # 填充数据到二维列表
    k_index = 0
    for line in file_lines:
        if line.startswith("|"):
            line_data = line.split("|")
            for j in range(len(line_data)):
                data_array[k_index][j] = line_data[j]
            k_index += 1
    
    # 数据清洗和转换
    for i in range(len(data_array)):
        for j in range(len(data_array[0])):
            # 去除首尾空格
            data_array[i][j] = data_array[i][j].strip()
            
            # 处理以"-"结尾的负数表示
            if data_array[i][j].endswith("-"):
                try:
                    data_array[i][j] = -float(data_array[i][j][:-1].replace(",", ""))
                except ValueError:
                    pass  # 如果转换失败，保持原值
            
            # 处理包含"币"的列，尝试转换为数值
            if i > 0 and ("币" in data_array[0][j] or "天1" in data_array[0][j] or "坏账计提比例（%）" in data_array[0][j] or "逾期天数" in data_array[0][j] or "账龄" in data_array[0][j]) and type(data_array[i][j]) == str:
                try:
                    data_array[i][j] = float(data_array[i][j].replace(",", ""))
                except (ValueError, TypeError):
                    pass  # 如果转换失败，保持原值
    
    return data_array
def classify(row):
    if row["认定类型描述"].startswith("其他应收款"):
        if row["天1"]>365:
            if row["到期日"]>"2026-6-30":
                return "长期应收款-长期应收款-信用减值损失"
            else:
                return "长期应收款-一年到期其他非流动资产-信用减值损失"
        else:
            return "其他应收款-信用减值损失"
    elif row["认定类型描述"].startswith("应收账款"):
            return "应收账款-信用减值损失"
    elif row["认定类型描述"].startswith("合同资产"):
        if row["天1"]>365 and "质保金" in row["认定类型描述"]:
            if row["到期日"]>"2026-6-30":
                return "合同资产-其他非流动资产-资产减值损失"
            else:
                return "合同资产-一年到期其他非流动资产-资产减值损失"
        else:
            return "合同资产-合同资产-资产减值损失"
    else:
        return "其他"

data=ReadTextFileToArray("C:\\Users\\<USER>\\Desktop\\坏账测试.txt")
df=pd.DataFrame(data[1:],columns=data[0])
df["类型"]=df.apply(classify,axis=1)
df.to_excel("C:\\Users\\<USER>\\Desktop\\坏账测试.xlsx",index=False)