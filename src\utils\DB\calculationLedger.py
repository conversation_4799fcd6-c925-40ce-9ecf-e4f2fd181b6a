import pandas as pd
import duckdb
import src.base.settings as settings
import src.utils.DB.outputSQL as outputSQL
import os
import openpyxl
from openpyxl.utils.dataframe import dataframe_to_rows
from src.utils.DB.configDB import configDB

ws=openpyxl.load_workbook(settings.PATH_CONFIG+r"\配置文件-资金.xlsx",read_only=True)["单据分类"]
docCodeDict={}
for row in ws.iter_rows(min_row=2,values_only=True):
    docCodeDict[row[0]]=row[1]

def if_list_elements(lst2: list,s:str): #资金分类函数
    d=s[:3]
    lst=str(lst2)
    if d in docCodeDict: 
        e=docCodeDict[d]
        if d == "CFK" and "劳务派遣" in lst:
            return "对外成本费用付款申请-薪酬"
        elif d == "SRL" and "待资金系统" in lst:
            return "收款确认单-退票"
        elif d == "JFK":
            if "劳务" in lst:
                return "支付劳务款"
            elif "购货" in lst:
                return "支付购货款"
            elif "分包" in lst:
                return "支付分包工程款"
            else :
                return "支付供应商其他"
        else:
            return e
    else:
        return ""

def getLedger(con:duckdb.DuckDBPyConnection,df:pd.DataFrame):
    try:
        # Test if function exists by trying to use it
        con.execute("SELECT if_classification(NULL, NULL)")
    except:
        con.create_function("if_classification", if_list_elements, [duckdb.list_type(type=str),str], str) #注册分类函数
    df=df
    con.execute(outputSQL.成本查询.replace("明细帐","df"))
    con.execute(outputSQL.内行查询.replace("明细帐","df"))
    queryNei=outputSQL.内行查询.replace("INSERT OR REPLACE INTO 内行查询","").replace("明细帐","df")
    dfnei=con.execute(queryNei).df()
    con.execute("insert or replace into 内行查询 select * from dfnei")
    con.execute(outputSQL.内行查询资金整理.replace("内行查询","dfnei"))
    con.execute(outputSQL.分供结算台账.replace("明细帐","df"))
    con.execute(outputSQL.付款台账.replace("明细帐","df"))
    con.execute(outputSQL.收款台账.replace("明细帐","df"))
    con.execute(outputSQL.专项储备.replace("明细帐","df"))
    con.execute(outputSQL.外部确权台账.replace("明细帐","df"))


    concatArray=configDB().internalCustomers
    if len(concatArray)==0:
        replaceCondition1="('总部客商名称')"
    elif len(concatArray)==1:
        replaceCondition1=f"('{concatArray[0][0]}')"
    else:
        concatArrayTuple=tuple(concatArray[i][0] for i in range(1,len(concatArray)))
        replaceCondition1=str(concatArrayTuple)
    replaceCondition2=configDB().expenseTransfer[0][0]
    replace2=outputSQL.按利润中心计算余额明细帐.replace("机关划转费用科目",replaceCondition2)
    replace2=replace2.replace("('总部客商名称')",replaceCondition1)
    df1=con.execute("select * from 一体化合同台账").df()
    df0=con.execute(replace2).df()
    df2=con.execute(outputSQL.应付汇总按供应商).df()
    df3=con.execute(outputSQL.应付汇总按合同).df()
    df4=pd.merge(df1,df3,on=['合同编号','项目名称'],how='left')
    con.execute(f"DROP TABLE IF EXISTS 总台账")
    con.execute(f"CREATE TABLE 总台账 AS SELECT * FROM df0")
    con.execute(f"DROP TABLE IF EXISTS 应付汇总按供应商")
    con.execute(f"CREATE TABLE 应付汇总按供应商 AS SELECT * FROM df2")
    con.execute(f"DROP TABLE IF EXISTS 应付汇总按合同")
    con.execute(f"CREATE TABLE 应付汇总按合同 AS SELECT * FROM df4")
    