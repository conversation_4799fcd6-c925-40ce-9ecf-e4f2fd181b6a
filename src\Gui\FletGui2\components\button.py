
import flet as ft
import src.Gui.callProcess as callF
from datetime import datetime
import src.base.cache as cache

def create_execute_button(page: ft.Page, button_text: str, params: list | None = None, button_func: str | None = None):
    if button_func is None:
        button_func = button_text

    # Nested click handler so we can evaluate param values at click-time
    def _handle_click(original_params: list | None):
        evaluated_params = []
        if original_params is not None:
            for p in original_params:
                # If the element has a runtime-changing value attribute (e.g. Dropdown) grab it
                try:
                    evaluated_params.append(p.value)
                except AttributeError:
                    evaluated_params.append(p)
        callF.thisProcess.run({"功能": button_func, "参数": evaluated_params})

    return ft.ElevatedButton(
        button_text,
        icon=ft.icons.PLAY_ARROW,
        on_click=lambda e: _handle_click(params),
        style=ft.ButtonStyle(
            color=ft.colors.WHITE,
            bgcolor=ft.colors.GREEN_500,
            padding=ft.padding.symmetric(horizontal=24, vertical=12),
            shape=ft.RoundedRectangleBorder(radius=6),
        ),
        width=240,
        height=44,
    )

def create_dropdown(page: ft.Page, options: list, default_value: str):
    dropdown = ft.Dropdown(
        width=200,
        options=[ft.dropdown.Option(opt) for opt in options],
        value=default_value,
        border_radius=8,
        filled=True,
        text_style=ft.TextStyle(size=14, color=ft.colors.BLUE_ACCENT)
    )
    
    def on_change(e):
        dropdown.value = e.control.value
        dropdown.update()
    
    dropdown.on_change = on_change
    return dropdown

def create_execute_button_with_dropdown_autoVouchr(page: ft.PageTransitionsTheme):
    dropdown = create_dropdown(page, ["使用已打开浏览器","自动打开浏览器"], "自动打开浏览器")
    return ft.Column(
        [
            ft.Text("自动制证", size=16, weight=ft.FontWeight.BOLD),
            ft.Row(
                [
                    dropdown,
                    create_execute_button(page, "自动制证",[dropdown]),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        ],
        spacing=10,)

def create_execute_button_with_dropdown_yes_no(page: ft.PageTransitionsTheme,button_text: str,textSize=16):
    yearList=[str(i) for i in range(2020,2030)]
    monthList=[str(i) for i in range(1,13)]
    nowmonth=datetime.now().month
    nowyear=datetime.now().year
    dropdown1=create_dropdown(page, yearList, str(nowyear))
    dropdown2=create_dropdown(page, monthList, str(nowmonth))
    return ft.Column(
        [
            ft.Text(button_text, size=textSize, weight=ft.FontWeight.BOLD),
            ft.Row(
                [
                    ft.Text("年份："),dropdown1,
                    ft.Text("月份："),dropdown2,
                    create_execute_button(page, button_text, [dropdown1, dropdown2]),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
            
        ],
        spacing=10,)

def download_plan(page: ft.PageTransitionsTheme):
    batch=["正常计划","追加计划第一次","追加计划第二次","所有批次"]
    batch_state=["流程中","结束","所有状态"]
    dropdown1=create_dropdown(page, batch, "正常计划")
    dropdown2=create_dropdown(page, batch_state, "流程中")
    return ft.Column(
        [
            ft.Text("下载计划", size=16, weight=ft.FontWeight.BOLD),
            ft.Row(
                [
                    ft.Text("选择下载批次："),dropdown1,
                    ft.Text("选择批次状态："),dropdown2,
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
                ft.Row(
                [
                    create_execute_button(page, "下载计划(重新开始)", [dropdown1, dropdown2]),
                    create_execute_button(page, "下载计划(失败继续)", [dropdown1, dropdown2]),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
            
        ],
        spacing=10,)

def upload_plan(page: ft.PageTransitionsTheme):
    batch=["正常计划","追加计划第一次","追加计划第二次"]
    dropdown1=create_dropdown(page, batch, "正常计划")
    return ft.Column(
        [
            ft.Text("上传计划", size=16, weight=ft.FontWeight.BOLD),
            ft.Row(
                [
                    ft.Text("选择批次："),dropdown1,
                    create_execute_button(page, "获取上传模版", [dropdown1]),
                    create_execute_button(page, "上报一体化计划", [dropdown1]),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER
            ),
            
        ],
        spacing=10,)

def create_execute_button_with_data_maintain(page: ft.PageTransitionsTheme):
    dropdown = create_dropdown(page, ["主数据","科目对照","异常数据","一体化合同台账","付款台账"], "科目对照")
    return ft.Column(
        [
            ft.Text("数据维护", size=16, weight=ft.FontWeight.BOLD),
            ft.Row(
                [
                    dropdown,
                    create_execute_button(page, "查询数据库",[dropdown]),
                    create_execute_button(page, "更新数据库",[dropdown]),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        ],
        spacing=10,)
    


def create_execute_button_with_row(page: ft.Page, title: str, button_text1: str, button_text2:str,button_text3:str):
    return ft.Column(
        [
            ft.Text(title, size=20, weight=ft.FontWeight.W_500, color=ft.colors.BLACK),
            ft.Row(
                [
                    create_execute_button(page, button_text1),
                    create_execute_button(page, button_text2),
                    create_execute_button(page, button_text3),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        ],
        spacing=10,
    )

def create_execute_button_with_text_row1(page: ft.Page, title: str, description: str, button_text1:str):
    return ft.Column(
        [
            ft.Text(title, size=20, weight=ft.FontWeight.W_500, color=ft.colors.BLACK),
            ft.Row(
                [
                    ft.Text(description,width=200,style=ft.TextStyle(size=14,color=ft.colors.BLUE_500)),
                    create_execute_button(page, button_text1),

                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        ],
        spacing=10,
    )



def create_execute_button_with_text_row2(page: ft.Page, title: str, description: str, button_text1:str,button_text2:str,func_list:list=None):
    if func_list is None:
        func_list=[button_text1,button_text2]
    return ft.Column(
        [
            ft.Text(title, size=20, weight=ft.FontWeight.W_500, color=ft.colors.BLACK),
            ft.Row(
                [
                    ft.Text(description,width=200,style=ft.TextStyle(size=14,color=ft.colors.BLUE_500)),
                    create_execute_button(page, button_text1,button_func=func_list[0]),
                    create_execute_button(page, button_text2,button_func=func_list[1]),

                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        ],
        spacing=10,
    )

def create_execute_button_with_row4(page: ft.Page, title: str, button_text0: str,button_text1: str, button_text2:str,button_text3:str):
    return ft.Column(
        [
            ft.Text(title, size=20, weight=ft.FontWeight.W_500, color=ft.colors.BLACK),
            ft.Row(
                [
                    create_execute_button(page, button_text0),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
            ft.Row(
                [
                    create_execute_button(page, button_text1),
                    create_execute_button(page, button_text2),
                    create_execute_button(page, button_text3),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        ],
        spacing=10,
    )

def report_compelete(page: ft.Page):
    filePicker1 = ft.FilePicker()
    def _handle_file_picker(e):
        filePicker1.pick_file()
    return ft.Column(
        [
            ft.Text("报告完成", size=20, weight=ft.FontWeight.W_500, color=ft.colors.BLACK),
            ft.Row(
                [
                    ft.Text("报告完成",width=200,style=ft.TextStyle(size=14,color=ft.colors.BLUE_500)),
                    create_execute_button(page, "报告完成"),
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        ],
        spacing=10,
    )

def create_execute_button_with_url(page: ft.Page, title: str, description: str, button_text1:str,url:str):
    return ft.Column(
        [
            ft.Text(title, size=20, weight=ft.FontWeight.W_500, color=ft.colors.BLACK),
            ft.Row(
                [
                    ft.Text(description,width=200,style=ft.TextStyle(size=14,color=ft.colors.BLUE_500)),
                    ft.ElevatedButton(
        button_text1,
        url=url,
        icon=ft.icons.PLAY_ARROW,
        style=ft.ButtonStyle(
            color=ft.colors.WHITE,
            bgcolor=ft.colors.GREEN_500,
            padding=ft.padding.symmetric(horizontal=24, vertical=12),
            shape=ft.RoundedRectangleBorder(radius=6),
        ),
        width=240,
        height=44,
    )

                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        ],
        spacing=10,
    )

class JiuQiReport(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        
        # 创建上年度报表路径选择器
        self.input_path = ft.TextField(
            label="选择输入文件",
            read_only=True,
            width=400,
            value='',
            border_radius=10,
            text_style=ft.TextStyle(size=14),
        )
        
        self.input_picker = ft.FilePicker(
            on_result=self.pick_input_file
        )
        
        self.input_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.input_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        # 创建本年度报表路径选择器
        self.config_path = ft.TextField(
            label="选择参数表",
            read_only=True,
            width=400,
            value='',
            border_radius=10,
            text_style=ft.TextStyle(size=14),
        )
        
        self.config_picker = ft.FilePicker(
            on_result=self.pick_config_file
        )
        
        self.config_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.config_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        self.commbox=ft.Dropdown(
            width=200,
            options=[
                ft.dropdown.Option("01-资产类"),
                ft.dropdown.Option("02-负债类"),
                ft.dropdown.Option("03-损益类"),
                ft.dropdown.Option("04-税务类"),
                ft.dropdown.Option("05-主表&权益类"),
                ft.dropdown.Option("06-关联方&分部报告"),
            ],
            color=ft.colors.BLUE_ACCENT,
            border_radius=10,
            filled=True,
            hint_text="请选择分组",
        )
        # 创建功能按钮
        
        self.audit_button = ft.ElevatedButton(
            "执行审核",
            icon=ft.icons.VERIFIED_USER,
            on_click=lambda e: callF.thisProcess.run({"功能": "批量审核","参数":[self.input_path.value,self.config_path.value,self.commbox.value]}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        self.controls = [
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("久其审核自动化小工具", size=20, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                        ft.Row(
                            controls=[
                                self.input_path,
                                self.input_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            controls=[
                                self.config_path,
                                self.config_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            controls=[
                                self.commbox,
                                self.audit_button
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        )
                    ],
                    spacing=10,
                ),
                padding=10,
                border_radius=7,
                margin=ft.margin.only(bottom=10),
            ),
            self.input_picker,
            self.config_picker,
        ]
    
    def pick_input_file(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.input_path.value = e.files[0].path
            self.input_path.update()
    
    def pick_config_file(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.config_path.value = e.files[0].path
            self.config_path.update()

class FinancialReportView(ft.Column):
    def __init__(self):
        super().__init__()
        self.visible = True
        self.expand = True
        
        # 创建上年度报表路径选择器
        self.last_year_path = ft.TextField(
            label="上年度报表路径",
            read_only=True,
            width=400,
            value=cache.LastYearFinancialReport,
            border_radius=10,
            border_color=ft.colors.OUTLINE_VARIANT,
            focused_border_color=ft.colors.PRIMARY,
            text_style=ft.TextStyle(size=14),
        )
        
        self.last_year_picker = ft.FilePicker(
            on_result=self.pick_last_year_file
        )
        
        self.last_year_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.last_year_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        # 创建本年度报表路径选择器
        self.current_year_path = ft.TextField(
            label="本年度报表路径",
            read_only=True,
            width=400,
            value=cache.CurrentYearFinancialReport,
            border_radius=10,
            text_style=ft.TextStyle(size=14),
        )
        
        self.current_year_picker = ft.FilePicker(
            on_result=self.pick_current_year_file
        )
        
        self.current_year_button = ft.ElevatedButton(
            "选择文件",
            icon=ft.icons.FOLDER_OPEN,
            on_click=lambda _: self.current_year_picker.pick_files(),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        # 创建功能按钮
        self.fill_button = ft.ElevatedButton(
            "执行初期数补全",
            icon=ft.icons.AUTOFPS_SELECT,
            on_click=lambda e: callF.thisProcess.run({"功能": "补全期初数"}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        self.audit_button = ft.ElevatedButton(
            "执行批量审核",
            icon=ft.icons.VERIFIED_USER,
            on_click=lambda e: callF.thisProcess.run({"功能": "批量审核"}),
            style=ft.ButtonStyle(
                shape=ft.RoundedRectangleBorder(radius=10),
                padding=15,
                text_style=ft.TextStyle(size=14),
            ),
        )
        
        self.controls = [
            ft.Container(
                content=ft.Column(
                    controls=[
                        ft.Text("财报助手", size=20, weight=ft.FontWeight.BOLD, color=ft.colors.PRIMARY),
                        ft.Row(
                            controls=[
                                self.last_year_path,
                                self.last_year_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            controls=[
                                self.current_year_path,
                                self.current_year_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                        ),
                        ft.Row(
                            controls=[
                                self.fill_button,
                                #self.audit_button,
                            ],
                            alignment=ft.MainAxisAlignment.SPACE_AROUND,
                        ),
                    ],
                    spacing=10,
                ),
                padding=10,
                border_radius=15,
                margin=ft.margin.only(bottom=10),
            ),
            self.last_year_picker,
            self.current_year_picker,
        ]
    
    def pick_last_year_file(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.last_year_path.value = e.files[0].path
            cache.LastYearFinancialReport = e.files[0].path
            cache.wirteLastYearFinancialReport()
            self.last_year_path.update()
    
    def pick_current_year_file(self, e: ft.FilePickerResultEvent):
        if e.files:
            self.current_year_path.value = e.files[0].path
            cache.CurrentYearFinancialReport = e.files[0].path
            cache.wirteCurrentYearFinancialReport()
            self.current_year_path.update()
