SELECT -sum(case when (明细帐.总账科目长文本 like '主营业务收入%' or 明细帐.总账科目长文本 like '其他业务收入%') and 过帐日期 >= '2025-01-01' then 明细帐.带符号的本位币金额 else 0 end) as 本年收入,
-sum(case when (科目分类2 like '%本年利润合计%') and 过帐日期 >= '2025-01-01' then 明细帐.带符号的本位币金额 else 0 end) as 本年利润,
sum(case when (明细帐.总账科目长文本 like '%存款%') then 明细帐.带符号的本位币金额 else 0 end) as 资金,
sum(case when (科目分类1 like '资产') then 明细帐.带符号的本位币金额 else 0 end) as 总资产,
sum(case when (科目分类1 like '负债') then 明细帐.带符号的本位币金额 else 0 end) as 总负债

from 明细帐 left join 科目对照 on 明细帐.总账科目长文本=科目对照.总账科目长文本


