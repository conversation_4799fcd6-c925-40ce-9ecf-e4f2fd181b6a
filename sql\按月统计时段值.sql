-- Active: 1749688970779@@127.0.0.1@3306
-- Active: 1748239704126@@127.0.0.1@3306

SELECT 
    DATE_TRUNC('month', 过帐日期) AS month,        -- 提取月份
    round(-SUM(case when 明细帐.总账科目长文本 like '主营业务收入%' then 明细帐.带符号的本位币金额 else 0 end)/10000,2) AS 当月收入,                     -- 当月收入
    round(-SUM(SUM(case when 明细帐.总账科目长文本 like '主营业务收入%' then 明细帐.带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 累计收入,
    round(-SUM(case when 科目分类2 like '%本年利润%' then 明细帐.带符号的本位币金额 else 0 end)/10000,2) AS 当月利润,   
    round(-SUM(SUM(case when 科目分类2 like '%本年利润%' then 明细帐.带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 累计利润,
    CASE EXTRACT(MONTH FROM DATE_TRUNC('month', 过帐日期))
    WHEN 1 THEN '一月'
    WHEN 2 THEN '二月'
    WHEN 3 THEN '三月'
    WHEN 4 THEN '四月'
    WHEN 5 THEN '五月'
    WHEN 6 THEN '六月'
    WHEN 7 THEN '七月'
    WHEN 8 THEN '八月'
    WHEN 9 THEN '九月'
    WHEN 10 THEN '十月'
    WHEN 11 THEN '十一月'
    WHEN 12 THEN '十二月'
  END AS 中文月份
FROM 明细帐 
left join 科目对照 on 明细帐.总账科目长文本=科目对照.总账科目长文本
where 过帐日期 >= '2025-01-01'
GROUP BY DATE_TRUNC('month', 过帐日期)
ORDER BY month
