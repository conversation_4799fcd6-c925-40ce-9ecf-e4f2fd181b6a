
from fastapi import HTTPException
from src.web.http import app  
from fastapi import Request
import logging
import traceback

@app.get("/api/report/metrics")
async def calculate_metrics():
    try:
        data = "写一个财务报告，中建三局财务报告，收入为1000万，成本为800万，利润为200万，再扩写一些内容"
        from src.utils.ai.ollama import get_ollama_response
        return get_ollama_response(data)
    except Exception as e:
        logging.error(f"calculate_metrics: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))
@app.get("/api/report/audit")
async def calculate_audit():
    try:
        data = "写一个稽核报告，中建三局，再扩写一些内容"
        from src.utils.ai.ollama import get_ollama_response
        return get_ollama_response(data)
    except Exception as e:
        logging.error(f"calculate_audit: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/report/fund")
async def calculate_fund():
    try:
        data = "写一个资金报告，中建三局，再扩写一些内容"
        from src.utils.ai.ollama import get_ollama_response
        return get_ollama_response(data)
    except Exception as e:
        logging.error(f"calculate_fund: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))