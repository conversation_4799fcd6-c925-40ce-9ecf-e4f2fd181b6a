#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用openpyxl将二维列表数据写入Excel文件
功能：
1. 打开Excel文件，在第一个工作表中查找标题行（大概在4-6行）
2. 将二维列表数据（第一行为标题）按标题匹配写入Excel
3. 如果Excel中没有对应标题，则跳过该列数据
"""

import openpyxl
from typing import List, Optional, Tuple


def find_header_row(worksheet, start_row: int = 4, end_row: int = 6) -> Optional[int]:
    """
    在指定范围内查找标题行
    
    Args:
        worksheet: openpyxl工作表对象
        start_row: 开始搜索的行号 (默认4)
        end_row: 结束搜索的行号 (默认6)
    
    Returns:
        标题行的行号，如果未找到返回None
    """
    for row_num in range(start_row, end_row + 1):
        # 检查该行是否有足够的非空单元格作为标题行
        non_empty_count = 0
        for col_num in range(1, worksheet.max_column + 1):
            cell_value = worksheet.cell(row=row_num, column=col_num).value
            if cell_value is not None and str(cell_value).strip():
                non_empty_count += 1
        
        # 如果该行有3个或更多非空单元格，认为是标题行
        if non_empty_count >= 3:
            return row_num
    
    return None


def get_header_mapping(worksheet, header_row: int) -> dict:
    """
    获取标题到列号的映射
    
    Args:
        worksheet: openpyxl工作表对象
        header_row: 标题行号
    
    Returns:
        标题名称到列号的字典映射
    """
    header_mapping = {}
    for col_num in range(1, worksheet.max_column + 1):
        cell_value = worksheet.cell(row=header_row, column=col_num).value
        if cell_value is not None:
            header_name = str(cell_value).strip()
            if header_name:
                header_mapping[header_name] = col_num
    
    return header_mapping


def write_list_to_excel(excel_file_path: str, data_list: List[List], 
                       sheet_index: int = 0, start_row_range: Tuple[int, int] = (4, 6)) -> bool:
    """
    将二维带标题的数据写入Excel文件
    
    Args:
        excel_file_path: Excel文件路径
        data_list: 二维列表，第一行为标题，后续行为数据
        sheet_index: 工作表索引，默认为0（第一个工作表）
        start_row_range: 标题行搜索范围，默认为(4, 6)
    
    Returns:
        成功返回True，失败返回False
    """
    try:
        # 检查数据格式
        if not data_list or len(data_list) < 1:
            print("错误：数据列表为空或格式不正确")
            return False
        
        # 第一行是标题
        data_headers = data_list[0]
        data_rows = data_list[1:] if len(data_list) > 1 else []
        
        print(f"数据中的标题：{data_headers}")
        print(f"数据行数：{len(data_rows)}")
        
        # 打开Excel文件
        workbook = openpyxl.load_workbook(excel_file_path)
        
        # 获取指定的工作表
        if sheet_index < len(workbook.worksheets):
            worksheet = workbook.worksheets[sheet_index]
        else:
            print(f"错误：工作表索引 {sheet_index} 超出范围")
            return False
        
        # 查找标题行
        header_row = find_header_row(worksheet, start_row_range[0], start_row_range[1])
        if header_row is None:
            print(f"错误：在第{start_row_range[0]}-{start_row_range[1]}行范围内未找到标题行")
            return False
        
        print(f"找到标题行：第{header_row}行")
        
        # 获取Excel中的标题映射
        excel_header_mapping = get_header_mapping(worksheet, header_row)
        print(f"Excel中的标题：{list(excel_header_mapping.keys())}")
        
        # 创建数据标题到Excel列的映射
        data_header_to_col = {}
        for i, header in enumerate(data_headers):
            if header in excel_header_mapping:
                data_header_to_col[i] = excel_header_mapping[header]
                print(f"匹配标题：'{header}' -> Excel列{excel_header_mapping[header]}")
            else:
                print(f"警告：数据标题 '{header}' 在Excel中不存在，将跳过")
        
        if not data_header_to_col:
            print("错误：没有找到匹配的标题")
            return False
        
        # 确定数据写入的起始行（标题行的下一行）
        data_start_row = header_row + 1
        
        # 写入数据
        for row_index, data_row in enumerate(data_rows):
            current_row = data_start_row + row_index
            
            # 确保数据行长度不超过标题长度
            for data_col_index, excel_col_num in data_header_to_col.items():
                if data_col_index < len(data_row):
                    value = data_row[data_col_index]
                    worksheet.cell(row=current_row, column=excel_col_num, value=value)
        
        # 保存文件
        workbook.save(excel_file_path)
        print(f"成功写入 {len(data_rows)} 行数据到 {excel_file_path}")
        return True
        
    except FileNotFoundError:
        print(f"错误：文件 {excel_file_path} 不存在")
        return False
    except Exception as e:
        print(f"错误：{str(e)}")
        return False


def create_test_excel():
    """创建一个测试用的Excel文件"""
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = "员工数据"
    
    # 添加一些前置内容
    worksheet['A1'] = "公司名称："
    worksheet['B1'] = "测试公司有限责任公司"
    worksheet['A2'] = "报表日期："
    worksheet['B2'] = "2024-01-01"
    worksheet['A3'] = "部门："
    worksheet['B3'] = "人力资源部"
    worksheet['A4'] = ""
    
    # 在第5行添加标题（模拟标题行在4-6行之间的情况）
    headers = ["员工编号", "姓名", "部门", "职位", "基本工资", "绩效奖金", "备注"]
    for col, header in enumerate(headers, 1):
        worksheet.cell(row=5, column=col, value=header)
    
    # 添加一些示例数据
    sample_data = [
        ["E001", "张三", "技术部", "软件工程师", 8000, 1000, "技术骨干"],
        ["E002", "李四", "销售部", "销售经理", 9000, 1500, "业绩优秀"],
        ["E003", "王五", "人事部", "人事专员", 6000, 800, "新员工"],
    ]
    
    for row_idx, row_data in enumerate(sample_data, 6):
        for col_idx, value in enumerate(row_data, 1):
            worksheet.cell(row=row_idx, column=col_idx, value=value)
    
    test_file = "员工数据表.xlsx"
    workbook.save(test_file)
    print(f"测试Excel文件已创建：{test_file}")
    return test_file


def main():
    """主函数 - 演示如何使用"""
    print("=== 二维列表写入Excel功能演示 ===\n")
    
    # 1. 创建测试Excel文件
    test_file = create_test_excel()
    
    # 2. 准备要写入的二维列表数据（第一行为标题）
    new_employee_data = [
        # 标题行 - 注意"身份证号"这个标题在Excel中不存在，会被跳过
        ["员工编号", "姓名", "部门", "职位", "基本工资", "绩效奖金", "备注", "身份证号"],
        # 数据行
        ["E004", "赵六", "财务部", "会计师", 7500, 1200, "注册会计师", "110101199001011234"],
        ["E005", "钱七", "技术部", "高级工程师", 12000, 2000, "技术专家", "110101199002022345"],
        ["E006", "孙八", "市场部", "市场专员", 6500, 900, "市场新人", "110101199003033456"],
    ]
    
    print("准备写入的数据：")
    print(f"标题：{new_employee_data[0]}")
    for i, row in enumerate(new_employee_data[1:], 1):
        print(f"  {i}. {row[1]} - {row[2]} - {row[3]}")
    
    print(f"\n开始写入数据到 {test_file}...")
    
    # 3. 调用写入函数
    success = write_list_to_excel(
        excel_file_path=test_file,
        data_list=new_employee_data,
        sheet_index=0,  # 第一个工作表
        start_row_range=(4, 6)  # 在第4-6行范围内查找标题行
    )
    
    if success:
        print(f"\n✅ 数据写入成功！请打开 {test_file} 查看结果")
        print("注意：'身份证号'列的数据不会被写入，因为Excel模板中没有这个标题")
    else:
        print("\n❌ 数据写入失败！")
    
    print("\n=== 演示完成 ===")


if __name__ == "__main__":
    main()
