import src.base.cache as cache
from python_calamine import CalamineWorkbook
from pathlib import Path
import openpyxl

def getIndexByTitle(inputList,title):
    if type(title)==str:
        for i in range(3):
            for j in range(len(inputList[i])):
                if inputList[i][j]==title:
                    return j
    elif type(title)==list:
        for i in range(len(title)):
            if i==0:
                for j in range(len(inputList[i])):
                    if inputList[i][j]==title[i]:
                        nextLoop = j
            else:
                for j in range(nextLoop,len(inputList[i])):
                    if inputList[i][j]==title[i]:
                        nextLoop = j
                        break
        if nextLoop is None:
            return 0
        return nextLoop
    else:
        return 0

def get_excel_data(file_path, sheet_name,titleList,amountTitle,keyIFtuple=True):
    workbook = CalamineWorkbook.from_path(file_path)
    theList = workbook.get_sheet_by_name(sheet_name).to_python()
    titleCol=[getIndexByTitle(theList,title) for title in titleList]
    if type(amountTitle)==int:
        amountCol=amountTitle
    else:
        amountCol=getIndexByTitle(theList,amountTitle)
    d={}
    for i in range(len(theList)):
        #判断是否为文本整数
        if type(theList[i][0]) is str and theList[i][0].lstrip('-').isdigit() and theList[i][0] != "" and theList[i][0] is not None:
            if keyIFtuple:
                key=tuple(theList[i][col] if theList[i][col] is not None else '' for col in titleCol)
                if type(theList[i][amountCol]) is not str and theList[i][amountCol] is not None: #如果为空字符则舍弃
                    d[key]=theList[i][amountCol]+d.get(key,0)
            else:
                key=''.join([theList[i][col] if theList[i][col] is not None else '' for col in titleCol])
                if type(theList[i][amountCol]) is not str and theList[i][amountCol] is not None: #如果为空字符则舍弃
                    key2=tuple(theList[i][col] if theList[i][col] is not None else '' for col in titleCol)
                    d[key]=[theList[i][amountCol]+d.get(key,[0])[0],key2]
    return d

def completBegingBalance(wb:openpyxl.Workbook,inputsheetname,titleList,amoutTitle,fillTitle,keyIftuple=True):
    d=get_excel_data(cache.LastYearFinancialReport,inputsheetname,titleList,amoutTitle,keyIftuple)
    ws=wb[inputsheetname]
    usedList=list(ws.values)
    titleCol=[getIndexByTitle(usedList,title) for title in titleList]
    fillCol=getIndexByTitle(usedList,fillTitle)+1
    fillList=[]
    startRow=0
    for i in  range(len(usedList)):
        if type(usedList[i][0]) is str and usedList[i][0].lstrip('-').isdigit() and usedList[i][0] != "":
            startRow=i+1 if usedList[i][0]=="1" else startRow
            if keyIftuple:
                key=tuple(usedList[i][col] for col in titleCol)
                fillList.append([d.get(key,0)])
                d[key]=0 #把已经填过的数据从d中删除,防止重复填,这样key重复出现也是填入0
            else:
                key=''.join([usedList[i][col] if usedList[i][col]!=None else '' for col in titleCol])
                fillList.append([d.get(key,[0])[0]])
                d[key]=[0] #把已经填过的数据从d中删除,防止重复填,这样key重复出现也是填入0


    #ws.cell(row=startRow,column=fillCol).value=fillList
    for row_idx, value in enumerate(fillList, start=startRow):
        for col_idx, value in enumerate(value, start=fillCol):
            ws.cell(row=row_idx, column=col_idx).value = value
    #开始补全表格中不存在的key
    usedRow=ws.max_row+1
    for key in d:
        if keyIftuple:
            if d[key]!=0:
                    for i in range(len(titleCol)):
                        ws.cell(row=usedRow,column=titleCol[i]+1).value=key[i]
        else:
            if d[key][0] !=0:
                fillkey=d[key][1]
                for i in range(len(titleCol)):
                    ws.cell(row=usedRow,column=titleCol[i]+1).value=fillkey[i]
                ws.cell(row=usedRow,column=fillCol).value=d[key][0]
                usedRow+=1
                
def fillLastTerm(theInput,rowStart=2):
    lastPath=cache.LastYearFinancialReport
    nowPath=cache.CurrentYearFinancialReport
    tablename=theInput[0]
    d_table={}
    #tablename2=d_table.get(tablename,tablename)
    workbook = CalamineWorkbook.from_path(lastPath)
    theList = workbook.get_sheet_by_name(tablename).to_python()
    wb=openpyxl.load_workbook(nowPath)
    ws=wb[tablename]
    d={}
    for item in theInput[1]:
        for i in range(rowStart-1,len(theList)):
                if len(theList[i])>=item[2] and len(theList[i])>=item[1]:
                    key=theList[i][item[0]-1]
                    if type(key)==str :
                        key=key.strip()
                        d[key]=theList[i][item[2]-1]

    for tiem in theInput[1]:
        for i in range(rowStart,ws.max_row):
            key=ws.cell(row=i,column=tiem[0]).value
            if type(key)==str:
                key=key.strip()
                ws.cell(row=i,column=tiem[1]).value=d.get(key.strip(),"去年表未找到同名该项目")
    newPath=nowPath.replace(".xlsx","_new.xlsx")
    wb.save(newPath)
    print("完成"+tablename+"期初数")


def complete():
    print("开始运行")
    wb=openpyxl.load_workbook(cache.CurrentYearFinancialReport)
    #注意
    completBegingBalance(wb,"FZ2116 其他应付款",["编码","名称","项目名称","款项性质","填报单位"],10,"年初余额",False)
    completBegingBalance(wb,"NBWL2022 内部往来-应付账款",["对方单位名称","科目","填报单位"],"期末余额","年初数")
    completBegingBalance(wb,"NBWL2004 内部往来-应收账款",["对方单位名称","科目","填报单位"],11,"年初数原值") #个别原位币哪里没填
    completBegingBalance(wb,"ZC2113 其他应收款",["编码","名称","坏账计提方式","客户类别","款项性质","填报单位"],"期末数","期初数",False)
    completBegingBalance(wb,"FZ2007 应付账款",["编码","名称","关联关系","项目名称","款项性质","填报单位"],["期末余额","账面金额","本位币"],"年初余额")
    completBegingBalance(wb,"NBWL2039 内部往来-跨法人往来",["对方单位","科目","本方单位"],"期末金额","年初金额（本位币）")
    completBegingBalance(wb,"NBWL2108 内部往来-其他应收款",["项目","款项性质","科目","填报单位"],"期末数","年初数原值")
    #筛选出
    all_sheet_names = wb.sheetnames
    sheets_to_keep = ["FZ2116 其他应付款","NBWL2022 内部往来-应付账款","NBWL2004 内部往来-应收账款","ZC2113 其他应收款","FZ2007 应付账款","NBWL2039 内部往来-跨法人往来"]
    # 确定要删除的工作表
    sheets_to_delete = [sheet_name for sheet_name in all_sheet_names if sheet_name not in sheets_to_keep]
    # 删除不需要的工作表
    for sheet_name in sheets_to_delete:
        del wb[sheet_name]
    newPath=cache.CurrentYearFinancialReport.replace(".xlsx","_new.xlsx")
    wb.save(newPath)
    print(f"新的文件保存在{newPath}")



def main():
    print("第二个版本运行")
    complete()
    #print("测试补全期初数")