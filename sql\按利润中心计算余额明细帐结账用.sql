select 
concat(明细帐.利润中心,明细帐.WBS元素) as 定位符,
any_value(主数据.利润中心组描述) as 利润中心组名称,
any_value(明细帐.利润中心描述) as 利润中心名称,
any_value(明细帐.WBS元素描述) as 项目名称,
any_value(明细帐.WBS元素) as 项目编码,
any_value(明细帐.利润中心) as 利润中心,
sum(case when 明细帐.总账科目长文本 like '主营业务收入%'  then 0-带符号的本位币金额 else 0 end) as 收入,
sum(case when 明细帐.总账科目长文本 like '主营业务成本%'  then 带符号的本位币金额 else 0 end) as 成本,
sum(case when 明细帐.总账科目长文本 like '主营业务收入%' and 过帐日期<$年初日期 then 0-带符号的本位币金额 else 0 end) as 年初收入,
sum(case when 明细帐.总账科目长文本 like '主营业务成本%' and 过帐日期<$年初日期  then 带符号的本位币金额 else 0 end) as 年初成本,
sum(case when 明细帐.总账科目长文本 like '专项储备%'  then 带符号的本位币金额 else 0 end) as 专项储备余额,
sum(case when 明细帐.总账科目长文本 like '%合同结算%' or 明细帐.总账科目长文本 like '合同资产\工程款（已完工未结算）' or 明细帐.总账科目长文本 like '%已结算未完工%' then 带符号的本位币金额 else 0 end) as 合同余额,
sum(case when 明细帐.总账科目长文本 like '合同履约成本%' and 明细帐.总账科目长文本 not like '合同履约成本%结转%' then 带符号的本位币金额 else 0 end) as 合同履约成本余额,
sum(case when 明细帐.总账科目长文本 like '%预计负债\亏损合同%'  then 带符号的本位币金额 else 0 end) as 预计负债亏损合同,
sum(case when 明细帐.总账科目长文本 like '原材料%'  then 带符号的本位币金额 else 0 end) as 原材料,
sum(case when 明细帐.总账科目长文本 like '合同履约成本%'  then 带符号的本位币金额 else 0 end) as 成本余额,
sum(case when 明细帐.总账科目长文本 like '应付账款%暂估%'  then 带符号的本位币金额 else 0 end) as 暂估应付余额,
sum(case when 明细帐.总账科目长文本 like '研发支出%'  then 带符号的本位币金额 else 0 end) as 研发支出,
sum(case when 明细帐.总账科目长文本 like '内部存款\非货币交易'  then 带符号的本位币金额 else 0 end) as 本利润非货币交易未平,
sum(case when 明细帐.总账科目长文本 like '%可用存款%' or 明细帐.总账科目长文本 like '银行存款%'  then 带符号的本位币金额 else 0 end) as 原始存量,
sum(case when 明细帐.总账科目长文本 like '%内部往来\内部借贷%'  then 带符号的本位币金额 else 0 end) as 内部借款,
sum(case when 明细帐.总账科目长文本 like '应付账款\应付供应链融资款'  then 带符号的本位币金额 else 0 end) as 保理借款,
sum(case when (明细帐.总账科目长文本 like '%内部往来\其他' or 明细帐.总账科目长文本 like '%内部往来\代垫费用')  and (客户描述 in ('总部客商名称')) then 带符号的本位币金额 else 0 end) as 内部往来挂总部,
sum(case when (明细帐.总账科目长文本 like '%内部往来\其他' or 明细帐.总账科目长文本 like '%内部往来\代垫费用') then 带符号的本位币金额 else 0 end) - 内部往来挂总部 as 内部往来挂经理部,
sum(case when 明细帐.总账科目长文本 like '机关划转费用科目' then 带符号的本位币金额 else 0 end) as 现场维护费,
内部往来挂经理部+现场维护费 as 内部往来需调整,
sum(case when ((明细帐.总账科目长文本 like '应收账款%进度%' or 明细帐.总账科目长文本 like '合同资产%质保金%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计确权,
0-sum(case when (明细帐.总账科目长文本 like '应收账款%进度%' and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计收款,
0-sum(case when ((明细帐.总账科目长文本 like '应付账款%' and 明细帐.总账科目长文本 not like '%税%' and 明细帐.总账科目长文本 not like '%暂估%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计分供结算,
sum(case when ((明细帐.总账科目长文本 like '应付账款%' and 明细帐.总账科目长文本 not like '%税%' and 明细帐.总账科目长文本 not like '%暂估%') and 文本 not like '%自动清账%' and  ((借贷标识 = 'S' AND 反记帐 = '') OR (借贷标识 = 'H' AND 反记帐 = 'X'))) then 带符号的本位币金额 else 0 end) as 累计分供付款,
sum(case when 明细帐.总账科目长文本 like '%分包工程支出%'  then 带符号的本位币金额 else 0 end) as 累计分包工程支出,
sum(case when 明细帐.总账科目长文本 like '%直接人工费%'  then 带符号的本位币金额 else 0 end) as 累计直接人工费,
sum(case when 明细帐.总账科目长文本 like '%直接材料费%'  then 带符号的本位币金额 else 0 end) as 累计直接材料费,
sum(case when 明细帐.总账科目长文本 like '%机械使用费%'  then 带符号的本位币金额 else 0 end) as 累计机械使用费,
sum(case when 明细帐.总账科目长文本 like '%其他直接费用%'  then 带符号的本位币金额 else 0 end) as 累计其他直接费用,
sum(case when (明细帐.总账科目长文本 like '%可用存款%' or 明细帐.总账科目长文本 like '银行存款%') and 过帐日期 < $年初日期 then 带符号的本位币金额 else 0 end) as 期初原始存量,
sum(case when 明细帐.总账科目长文本 like '%内部往来\内部借贷%' and 过帐日期 <$年初日期  then 带符号的本位币金额 else 0 end) as 期初内部借款,
sum(case when 明细帐.总账科目长文本 like '应付账款\应付供应链融资款' and 过帐日期 < $年初日期  then 带符号的本位币金额 else 0 end) as 期初保理借款,
sum(case when 明细帐.总账科目长文本 like '%内部往来\其他' and (客户描述 in ('总部客商名称')) and 过帐日期 <$年初日期 then 带符号的本位币金额 else 0 end) as 期初内部往来挂总部,
期初原始存量+期初内部借款+期初保理借款+期初内部往来挂总部 as 期初快速资金存量,
原始存量+内部借款+保理借款+内部往来挂总部 as 快速资金存量,
累计收款 as 开累资金流入,
累计收款-快速资金存量 as 开累资金流出,
0-sum(case when ((明细帐.总账科目长文本 like '应收账款%进度%' and 文本 not like '%自动清账%' and  ((借贷标识 = 'H' AND 反记帐 = '') OR (借贷标识 = 'S' AND 反记帐 = 'X'))) ) and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期收款,
期初快速资金存量+本期收款-快速资金存量 as 本期流出,
0-sum(case when 科目分类2 like '%利润%' then 带符号的本位币金额 else 0 end) as 未含本期毛利累计利润,
0-sum(case when 科目分类2 like '%利润%' and 过帐日期 < $年初日期 then 带符号的本位币金额 else 0 end) as 年初累计利润,
sum(case when (明细帐.总账科目长文本 like '%合同结算%' or 明细帐.总账科目长文本 like '合同资产\工程款（已完工未结算）' or 明细帐.总账科目长文本 like '%已结算未完工%') and 过帐日期<$年初日期 then 带符号的本位币金额 else 0 end) as 年初合同余额,
sum(case when 明细帐.总账科目长文本 like '%分包工程支出%' and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期分包工程支出,
sum(case when 明细帐.总账科目长文本 like '%直接人工费%' and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期直接人工费,
sum(case when 明细帐.总账科目长文本 like '%直接材料费%' and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期直接材料费,
sum(case when 明细帐.总账科目长文本 like '%机械使用费%'  and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期机械使用费,
sum(case when 明细帐.总账科目长文本 like '%其他直接费用%'  and 过帐日期 >=$年初日期 then 带符号的本位币金额 else 0 end) as 本期其他直接费用
FROM 明细帐 
LEFT JOIN 科目对照 on 明细帐.总账科目长文本=科目对照.总账科目长文本  
left join 主数据 on 明细帐.利润中心 = 主数据.利润中心
where  过帐日期<=$期末日期 and WBS元素 != '' and WBS元素 not like 'QCQH%'
GROUP by  明细帐.利润中心,明细帐.WBS元素

