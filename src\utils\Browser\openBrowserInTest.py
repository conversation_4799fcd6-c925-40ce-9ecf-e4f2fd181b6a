
import sys
import os
from playwright.sync_api import Playwright, sync_playwright,Page
import json
import src.utils.cscec as cscec
import time
import src.base.settings as settings
thePath=settings.PATH_INTERNAL

def openDebugModeBrowser(s0=None,s1=None,s2=None,s3=None):
    try:
        with sync_playwright() as playwright:
            browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
            default_context = browser.contexts[0]
            page = cscec.switch_to_page(default_context,"中国建筑司库一体化")
            
    except:
        import subprocess
        chromeScript="start Chrome --remote-debugging-port=9222 --remote-allow-origins=*  --user-data-dir=C:\chromeDataForUser "
        if s0=="edge":
            subprocess.call(thePath+"/cache/RunEdge9222.bat",shell=True)
        else:
            subprocess.call(chromeScript,shell=True)
        ''' time.sleep(5)
        with sync_playwright() as playwright:
            browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
            default_context = browser.contexts[0]
            page=default_context.new_page()
            page.goto(f"https://iam.cscec.com/cas/login?service=https%3A%2F%2Ffip.cscec.com%2FOSPPortal%2Fcallback")
            page.locator("//label[text()='我的单据']").click(timeout=360000) #通过六分钟等待'''

def insertJS():
    with sync_playwright() as playwright:
        browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
        default_context = browser.contexts[0]
        page = cscec.switch_to_page(default_context,"中国建筑司库一体化")
        # 定义要注入的 JavaScript 代码
        script_content = """
        (() => {
            // 创建条幅容器
            const banner = document.createElement('div');
            banner.style.position = 'fixed';
            banner.style.top = '0';
            banner.style.left = '0';
            banner.style.width = '100%';
            banner.style.backgroundColor = '#f0f0f0';
            banner.style.padding = '10px';
            banner.style.boxShadow = '0px 4px 6px rgba(0, 0, 0, 0.1)';
            banner.style.zIndex = '9999';
            banner.style.display = 'flex';
            banner.style.alignItems = 'center';
            banner.style.gap = '10px';

            // 创建输出区
            const output = document.createElement('div');
            output.style.flex = '1';
            output.style.border = '1px solid #ccc';
            output.style.padding = '5px';
            output.style.minHeight = '30px';
            output.style.overflowY = 'auto';
            output.textContent = '等待操作...';

            // 创建按钮
            const button = document.createElement('button');
            button.textContent = '获取并发送';
            button.style.padding = '5px 10px';
            button.style.cursor = 'pointer';

            // 将元素添加到条幅中
            banner.appendChild(output);
            banner.appendChild(button);

            // 将条幅插入到页面顶部
            document.body.prepend(banner);

            // 按钮点击事件
            button.addEventListener('click', async () => {
                // 获取所有 input 的值
                const inputs = Array.from(document.querySelectorAll('input'))
                    .map(input => input.value)
                    .filter(value => value.trim() !== '');

                if (inputs.length === 0) {
                    output.textContent = '未找到任何输入值！';
                    return;
                }

                // 显示正在处理的状态
                output.textContent = '正在发送请求...';

                try {
                    // 发送 POST 请求
                    const response = await fetch('http://127.0.0.1:8000/api/flowID', { // 替换为你的目标服务器地址
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ inputs }) // 发送的数据
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const result = await response.json();
                    output.textContent = `服务器返回: ${JSON.stringify(result)}`;
                } catch (error) {
                    output.textContent = `请求失败: ${error.message}`;
                }
            });
        })();
        """

        # 使用 addScriptTag 注入脚本
        page.add_script_tag(content=script_content)

