import src.Gui.callProcess as callF
import flet as ft
from src.Gui.FletGui2.views import ui_constants as C

def create_setting_tab(page: ft.Page):
    machine_code_field = ft.TextField(
        value=callF.regInstance.machine_code,
        read_only=True,
        label="本机机器码",
        expand=True,
        border_radius=10,
        border_color=ft.colors.OUTLINE_VARIANT,
        focused_border_color=ft.colors.PRIMARY,
        text_style=ft.TextStyle(size=14),
    )
    registerText = ft.TextField(
        value="",
        min_lines=1,
        max_lines=1,
        text_size=14,
        label="输入注册码",
        expand=True,
        border_radius=10,
        border_color=ft.colors.OUTLINE_VARIANT,
        focused_border_color=ft.colors.PRIMARY,
    )
    register_button = ft.ElevatedButton(
        "注册",
        icon=ft.icons.APP_REGISTRATION,
        style=ft.ButtonStyle(
            shape=ft.RoundedRectangleBorder(radius=10),
            padding=15,
            text_style=ft.TextStyle(size=14),
        ),
    )
    expire_time_text = ft.Text(
        f"本机到期时间：{callF.regInstance.expire_time}",
        size=16,
        weight=ft.FontWeight.BOLD,
    )
    def sysRegister(e):
        try:
            callF.regInstance.register(registerText.value)
            expire_time_text.value = f"本机到期时间：{callF.regInstance.expire_time}"
            expire_time_text.update()
        except Exception as e:
            page.show_snack_bar(ft.SnackBar(ft.Text(str(e)),ft.colors.RED))
    register_button.on_click = sysRegister
    
    
    return ft.Container(
        content=ft.Column(
            controls=[
                machine_code_field,
                registerText,
                register_button,
                expire_time_text,
                ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
            ],
            expand=True,
            scroll=ft.ScrollMode.AUTO,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            alignment=ft.MainAxisAlignment.START,
            spacing=20,
        ),
        padding=ft.padding.symmetric(vertical=20, horizontal=30),
        alignment=ft.alignment.top_center,
        expand=True,
        bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
        border_radius=8,
        margin=ft.margin.all(5),
    )