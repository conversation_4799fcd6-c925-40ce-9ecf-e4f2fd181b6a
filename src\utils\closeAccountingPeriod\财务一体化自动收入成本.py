
import src.utils.cscec as cscec
from playwright.sync_api import Playwright, sync_playwright,Page
import win32com.client
import src.utils.Browser.Browser as browser
import src.base.settings as settings
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd
from src.utils.common import checkStr
def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s
def autofill():
    db=excelDB()
    df=db.getDataframe("独立结账模板收入成本")
    page = browser.myBrowser("cscec").page
    print(f"共{df.index.size}行")
    for i,row in df.iterrows():
            d=row.to_dict()
            if d["一体化收入确认是否"]=="是":
                print(f"开始执行{i+1}行")
                db.updateData("独立结账模板收入成本","一体化收入确认是否","开始执行",i+1)
                cscec.changeProjectCscec(page,d["组织机构"],d["项目名称"])
                #切换项目完成
                page.locator("//*[@id='root']/section/section/aside/div/div/div[2]/div/div[1]/ul[3]/li/span").click()
                page.locator("//span[contains(text(),'收入及收款')]").click()
                page.locator("//span[contains(@class,'txt')][contains(text(),'收入确认单-施工\投资项目')]").click() 
                
                
                page.locator(label_question("合同名称")).click()
                page.get_by_placeholder("请输入查询合同编号关键字").fill(d["收入合同编号"])
                page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
                page.get_by_text(d["收入合同编号"]).first.dblclick() #直接双击确定合同  
                if "在建" in d["项目状态"] or "在施" in d["项目状态"]:
                    project_status="在施"
                elif "未结"  in d["项目状态"]:
                    project_status="已竣未结"
                else:
                    project_status="已竣已结"
                page.locator(label_question("项目状态")).click()
                page.get_by_text(project_status,exact=True).click()
                
                if True:
                    page.get_by_placeholder("事由不能超过").fill(d["事由"])
                    file_path=settings.PATH_DOWNLOAD+"/收入匡算附件/"+d["利润中心编码"]+".xlsx"
                    page.locator("//span[text()='导入']/parent::div/parent::div/parent::div/parent::div").click()
                    with page.expect_file_chooser() as fc_info:
                        page.locator("//*[text()='自定义导入']/parent::div/parent::div/parent::div//div[contains(text(),'浏览')]/parent::div/parent::div/parent::div/preceding-sibling::input").click() #点击input才有效果
                        file_chooser = fc_info.value
                        file_chooser.set_files(file_path)
                        page.locator("//*[text()='自定义导入']/parent::div/parent::div/parent::div//div[text()='导入']").click() 
                        cscec.clickDigalog(page,"提示信息")
                else:
                    page.get_by_placeholder("事由不能超过").fill(d["项目简称"]+str(int(ws.Cells(2,2).Value))+"年"+str(int(ws.Cells(3,2).Value))+"月"+"收入确认") #此处还有些区别,查完合同在写事由
                    cscec.fillLalbel_input(page,"合同预计总收入",str(d["结账预计收入"]))
                    cscec.fillLalbel_input(page,"合同预计总成本",str(d["结账预计成本"]))
                    cscec.fillLalbel_input(page,"合同预计毛利：",str(d["结账预计收入"]-d["结账预计成本"]))
                    cscec.fillLalbel_input(page,"合同预计毛利率",str((d["结账预计收入"]-d["结账预计成本"])/d["结账预计收入"]))

                    cscec.fillLalbel_input(page,"账面累计实际成本",str(d["按比例结账成本"]))
                    cscec.fillLalbel_input(page,"完工进度",str(d["按比例结账成本"]/d["结账预计成本"]))

                    cscec.fillLalbel_input_2(page,"开工至本期累计 应确认的合同收入",str(d["按比例结账收入"]))
                    cscec.fillLalbel_input_2(page,"以前会计期间累计 已确认的合同收入",str(d["已过帐主营业务收入"]))
                    cscec.fillLalbel_input_2(page,"当期应确认的 合同收入",str(d["本期收入"]))

                    cscec.fillLalbel_input_2(page,"开工至本期累计 应确认的合同成本",str(d["按比例结账成本"]))
                    cscec.fillLalbel_input_2(page,"前会计期间累计 已确认的合同成本",str(d["已过帐主营业务成本"]))
                    cscec.fillLalbel_input_2(page,"当期应确认的 合同成本",str(d["本期成本"]))

                    cscec.fillLalbel_input_2(page,"开工至本期累计 的合同毛利",str(d["按比例结账收入"]-d["按比例结账成本"]))
                    cscec.fillLalbel_input_2(page,"以前会计期间累计 已确认的合同毛利",str(d["已过帐主营业务收入"]-d["已过帐主营业务成本"]))
                    cscec.fillLalbel_input_2(page,"当期应确认的 合同毛利",str(d["本期收入"]-d["本期成本"]))

                    cscec.fillLalbel_input_2(page,"开工至本期累计 应确认的合同预计损失","0")
                    cscec.fillLalbel_input_2(page,"以前会计期间 确认的合同预计损失","0")
                    cscec.fillLalbel_input_2(page,"当期合同的 预计损失","0")
                    
                    cscec.fillLalbel_input(page,"累计工程结算",str(d["累计确权"]))
                    
                    if d["结账后已完工未结算（负数为合同负债）"]>0:
                        cscec.fillLalbel_input(page,"已完工未结算 （合同资产）",str(d["结账后已完工未结算（负数为合同负债）"]))
                        cscec.fillLalbel_input(page,"已结算未完工 （合同负债","0")
                    else:
                        cscec.fillLalbel_input(page,"已结算未完工 （合同负债",str(d["结账后已完工未结算（负数为合同负债）"]))
                        cscec.fillLalbel_input(page,"已完工未结算 （合同资产）","0")

                    cscec.fillLalbel_input_2(page,"开工至本期累计 净利润",str(d["累计利润"]))
                    cscec.fillLalbel_input(page,"本年净利润",str(d["累计利润"]-d["年初累计利润"]))
                    cscec.fillLalbel_input(page,"本月净利润",str(d["本期毛利"]))  #不是哪么好算，暂用这个代替

                try:
                    project_Progress=round(float(page.locator("//label[contains(text(),'完工进度')]/parent::div/following-sibling::div[1]//input").input_value().strip('%'))/100,4)             
                except:
                    project_Progress=0.05 #防止系统因为进度为0而报错，项目实际成本远小于预计成本，例如10/1亿就会出问题
                if ("在建" in d["项目状态"] or "在施" in d["项目状态"])  and project_Progress>=0.95:
                    project_Progress="0.94"
                elif "未结" in d["项目状态"] and project_Progress<=0.95:
                    project_Progress="0.96"
                elif "已结" in d["项目状态"]:
                    project_Progress="1"
                else:
                    project_Progress=str(project_Progress)
                page.locator("//label[contains(text(),'完工进度')]/parent::div/following-sibling::div[1]//input").click()
                page.locator("//label[contains(text(),'完工进度')]/parent::div/following-sibling::div[1]//input").fill(project_Progress)
                page.locator("//label[contains(text(),'完工进度')]/parent::div").dblclick()
                #修正完成

                

                s="//div[contains(text(),'收支类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[3]"
                page.locator(s).dblclick()
                cscec.getInputAbove(page,s).fill(str(d["结账预计收入"])) #修正预计收入

                s="//div[contains(text(),'收支类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[7]/td[3]"
                page.locator(s).dblclick()
                cscec.getInputAbove(page,s).fill(str(d["结账预计收入"])) #修正预计收入
                
                s="//div[contains(text(),'收支类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[8]/td[3]"
                page.locator(s).dblclick()
                cscec.getInputAbove(page,s).fill(str(d["结账预计成本"])) #修正预计成本

                s="//div[contains(text(),'收支类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[8]/td[4]"
                page.locator(s).dblclick() #回点确认数据

                page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()
                cscec.getVisible(page,"//span[text()='提交']").click() #通过保存来确定提交按钮
                def fillapprover(rowint,approvalSelectorTable,personCode:str):
                    personCode=checkStr(personCode)
                    approvalSelectorTable.clickInputQuery(rowint, "人员编号")
                    cscec.fillLalbel_input(page, "人员编号或名称", personCode)
                    cscec.getVisible(page, "//div[text()='查询']").click()
                    cscec.locatorDigalog(page, "审核人").locator(f"//div[text()='{personCode}']").dblclick()
                if d['审批人1']!="" and d['审批人1'] is not None and (not pd.isna(d['审批人1'])):
                    approvalSelectorTable = cscec.cscecTable(page, "流程节点")
                    fillapprover(1,approvalSelectorTable,d['审批人1'])
                    if d['审批人2']!="" and d['审批人2'] is not None and (not pd.isna(d['审批人2'])):
                        fillapprover(2, approvalSelectorTable, d[ '审批人2'])
                    if d['审批人3']!="" and d['审批人3'] is not None and (not pd.isna(d['审批人3'])):
                        fillapprover(3, approvalSelectorTable, d[ '审批人3'])
                    cscec.clickDigalog(page, "设置节点审批人")

                page.locator("//*[text()='处理意见']/parent::div/parent::div/parent::div//div[text()='确定']").click()
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click()

                s="//label[contains(text(),'单据编号')]/parent::div/following-sibling::div[1]//label"
                document_number=page.locator(s).text_content()
                db.updateData("独立结账模板收入成本","匡算单据号",document_number,i+1)
                page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click() #关闭结算单
                db.updateData("独立结账模板收入成本","一体化收入确认是否","执行完毕",i+1)
  
