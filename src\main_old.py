import sys
import os
import logging
import flet as ft
#import src.Gui.callProcess #导入开始创建函数进程
from src.Gui.FletGui.overall_view import overallView
from src.Gui.FletGui.welcome_screen import WelcomeScreen
#from src.Gui.FletGui.register_dialog import RegisterDialog
#from src.Gui.register import check_register_status
import multiprocessing
import src.base.settings as setting
import requests
from src.web.http import start_server  # 导入服务器启动函数
version = "2.3.1"
#logging.basicConfig(level=logging.INFO)


def main(page: ft.Page):
    page.locale_configuration = ft.LocaleConfiguration(
        supported_locales=[
            ft.Locale("zh", "CN", "Hans"),
        ],
        current_locale=ft.Locale("zh", "CN", "Hans"),
    )
    page.window.icon = setting.CACHE_PATH+"/rpa.ico"
    #page.theme = ft.Theme(font_family="Microsoft YaHei")
    page.title = "信小财"
    page.window.center()

    # 显示欢迎界面
    welcome_screen = WelcomeScreen()

    def on_welcome_complete():
        # 欢迎界面完成后，显示主界面
        page.clean()
        setup_main_interface()

    def setup_main_interface():
        # 创建主界面
        theOverallView=overallView()
        def route_change(e):
            print(page.route)
            theOverallView.changeMiddleView(page.route[1:])
        studyUrl="https://q2t4357ads.feishu.cn/wiki/H2GHwBZjFiIkF4kKxFTclmTanTg?from=from_copylink"
        page.appbar = ft.AppBar(
            leading=ft.Container(content=ft.TextButton("操作手册",on_click=lambda e:os.system(f'''"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe" {studyUrl}'''))),
            leading_width=300,
            title=ft.Row(
                controls=[
                    ft.Text("信小财RPA财务机器人"),
                    ft.Container(
                        padding=10,
                        content=ft.Text(
                            f"v{version}",
                            size=14,
                            weight=ft.FontWeight.W_500,
                            color=ft.colors.SECONDARY,
                            style=ft.TextStyle(
                                font_family="Microsoft YaHei",
                                letter_spacing=0.5
                            )
                        )
                    )
                ],
                alignment=ft.MainAxisAlignment.CENTER,
            ),
            center_title=True,
            bgcolor=ft.colors.INVERSE_PRIMARY,
            actions=[
                ft.Container(
                    padding=10,
                    content=ft.Image(
                        src=setting.PATH_INTERNAL+"/assets/slogan.svg",
                        width=100,
                        height=30,
                        fit=ft.ImageFit.CONTAIN,
                    )
                )
            ],
        )
        page.window.height=730
        page.window.width=1200
        page.theme_mode = ft.ThemeMode.LIGHT
        page.on_error = lambda e: print("Page error:", e.data)
        page.on_route_change = route_change
        page.add(theOverallView)

        theOverallView.changeMiddleView("统计大师")

        # 检查注册状态和版本更新
        check_registration_and_version()

    def check_registration_and_version():
        server_process = start_server() #先启动fastapi服务 主程序结束时，服务器进程会自动关闭（因为设置了daemon=True）
        # 版本检查逻辑
        import src.Gui.callProcess #导入开始创建函数进程
        from src.Gui.FletGui.register_dialog import RegisterDialog
        from src.Gui.register import check_register_status
        if not check_register_status():
            # 创建注册弹窗
            register_dialog = RegisterDialog(on_register_success=lambda: page.update())
            page.dialog = register_dialog
            page.dialog.open = True
            page.update()
        try:
            resp = requests.post(
                "http://k8stest.cscec3b-iti.com/g3-fip-web/version/queryLatestVersion",
                timeout=5
            )
            if resp.status_code == 200:
                data = resp.json()
                if data.get("code") == 0 and data.get("success") and data.get("data"):
                    remote = data["data"]
                    remote_version = remote.get("versionCode", "")
                    remote_desc = remote.get("versionDesc", "")
                    # 仅比较数字部分
                    def version_tuple(v):
                        return tuple(int(x) for x in v.lstrip('vV').split('.') if x.isdigit())
                    if version_tuple(remote_version) > version_tuple(version):
                        dlg = ft.AlertDialog(
                            title=ft.Text(f"可获取的新版本#{remote_version}"),
                            content=ft.Text(remote_desc),
                            actions=[ft.TextButton("知道了", on_click=lambda e: (setattr(page.dialog, "open", False), page.update()))],
                            open=True
                        )
                        page.dialog = dlg
                        page.dialog.open = True
                        page.update()
        except Exception as e:
            print(f"版本检查失败: {e}")

    # 启动欢迎界面
    welcome_screen.show(page, on_welcome_complete)

if __name__ == "__main__":  
    multiprocessing.freeze_support()
    ft.app(main)
