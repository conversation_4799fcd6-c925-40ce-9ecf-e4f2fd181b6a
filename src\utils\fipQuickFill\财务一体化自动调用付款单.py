
import time
from playwright.sync_api import Playwright, sync_playwright,Page
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd

def label_question(s):
    s="//label[contains(text(),'"+s+"')]/parent::div/following-sibling::div[1]/div/div/div"
    return s


def autoOne(page:Page,d: dict):  
    cscec.changeProjectCscec(page,d["组织机构"],d["项目名称"])
                    #先切换项目
    page.locator("//*[@id='root']/section/section/aside/div/div/div[2]/div/div[1]/ul[3]/li/span").click()
    page.locator("//span[contains(text(),'应付及付款')]").click()
    if d["分包类型"].find("劳务")>-1:
        page.locator("//span[contains(@class,'txt')][contains(text(),'支付劳务款')]").click() 
    elif d["分包类型"].find("专业")>-1:
        page.locator("//span[contains(@class,'txt')][contains(text(),'支付分包工程款')]").click() 
    elif d["分包类型"].find("材料")>-1:
        page.locator("//span[contains(@class,'txt')][contains(text(),'支付购货款')]").click() 
    
    
    page.get_by_placeholder("事由不能超过").fill(d["事由"]) 
    page.locator(label_question("合同名称")).first.click()
    page.get_by_placeholder("请输入查询合同编号关键字").fill(d["合同编号"])
    page.locator("//*[text()='合同帮助']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
    page.get_by_text(d["合同编号"]).first.dblclick()
    page.get_by_placeholder("事由不能超过").fill(d["事由"])
    #table点击
    s="//div[contains(text(),'本次申请支付金额')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[7]"
    page.locator(s).click()
    cscec.getInputAbove(page,s).fill(str(d["付款金额"])) #

    if d["扣款金额"]!=None and d["扣款金额"]>0:
        s="//div[contains(text(),'扣款类型')]//parent::span/parent::div/parent::td/preceding-sibling::td[2]" #通过科目编号找到增加按钮
        page.locator(s).click()
        page.get_by_text("增加").click()
        s="//div[contains(text(),'扣款类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[3]"
        page.locator(s).click()
        time.sleep(0.2)
        cscec.clickMouse(page,s,95,50) #点击问号
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//div[text()='其他']").dblclick()
        
        time.sleep(0.5)
        s="//div[contains(text(),'扣款类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[4]"
        page.locator(s).click()
        time.sleep(0.1)
        page.locator(s).click()
        page.locator(s).click()
        cscec.clickMouse(page,s,96.5,50) #点击问号

        if d["分包类型"].find("劳务")>-1:
            page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//div[text()='工程施工成本\直接人工费']").dblclick()
        elif d["分包类型"].find("专业")>-1:
            page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//div[text()='分包工程支出-非甲指']").dblclick()
        elif d["分包类型"].find("材料")>-1:
            page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//div[text()='工程施工成本\直接材料费']").dblclick()
        
        s="//div[contains(text(),'扣款类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[7]"
        page.locator(s).click()
        cscec.clickMouse(page,s,95,50) #点击问号
        page.get_by_placeholder("请输入查询关键字").fill("零税率")
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//td/div[text()='零税率']").first.dblclick()

        s="//div[contains(text(),'扣款类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[5]"
        page.locator(s).dblclick()
        cscec.getInputAbove(page,s).fill(str(d["扣款事由"])) #调整金额0.01

        s="//div[contains(text(),'扣款类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[6]"
        page.locator(s).dblclick()
        cscec.getInputAbove(page,s).fill(str(d["扣款金额"])) #调整金额0.01
    
    if isinstance(d["附件"],str) :
        page.locator("//span[text()='附件']/parent::div/parent::div/parent::div/parent::div").click()
        with page.expect_file_chooser() as fc_info:
            page.locator("//*[text()='附件窗口']/parent::div/parent::div/parent::div//span[contains(text(),'上传')]").click() 
            file_chooser = fc_info.value
            file_chooser.set_files(d["附件"].split(";"))
            try:
                page.locator("//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']").click(timeout=300000)  
            except:
                cscec.clickMouse(page,"//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']",50,50).click()
                cscec.clickMouse(page,"//*[text()='系统提示']/parent::div/parent::div/parent::div//div[text()='确定']",50,50).click() #两个都是可见的
                
            page.locator("//*[text()='附件窗口']/preceding-sibling::div[1]//td[3]").click()

    if d["付款方式"]!="银行存款":
        s="//label[contains(text(),'支付方式')]/parent::div/following-sibling::div[1]/div/div/div"
        page.locator(s).click()
        page.get_by_placeholder("请输入查询关键字").fill(d["付款方式"])
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div").get_by_text(d["付款方式"],exact=True).first.dblclick()
    
    if isinstance(d["实际支付单位"],str) :
        s="//label[contains(text(),'实际支付单位')]/parent::div/following-sibling::div[1]/div/div/div"
        page.locator(s).click()
        page.get_by_placeholder("请输入查询关键字").fill(d["实际支付单位"])
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//span[contains(text(),'查询')]/parent::div/parent::div").click()
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div").get_by_text(d["实际支付单位"],exact=True).first.dblclick()

        s="//label[contains(text(),'实际支付项目')]/parent::div/following-sibling::div[1]/div/div/div"
        page.locator(s).click()
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//td/div/div[text()='1']").dblclick()

    if d["付款方式"]=="内行存款":
        s="//div[contains(text(),'收款账号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[4]"
        page.locator(s).click() #上面与实际不一致
        cscec.clickMouse(page,s,95,50) #点击问号
        page.get_by_text("收款方选用").click()
        s="//input[@placeholder='请输入查询的内容如：单位编号、单位名称。']"
        cscec.getVisible(page,s).fill(d["内行收款编码或项目名称"])
        time.sleep(0.5)
        s="//*[text()='收款方帮助']/parent::div/parent::div/parent::div//img[contains(@src,'查询')]/parent::div"
        cscec.getVisible(page,s).click()
        cscec.getVisible(page,s).click()
        page.locator("//td[@title='"+d["内行收款编码或项目名称"]+"']").dblclick() #选择客商
        
        s="//div[contains(text(),'收款账号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[5]"
        cscec.getVisible(page,s).dblclick()
        cscec.clickMouse(page,s,95,50) #点击问号
        page.locator("//*[text()='帮助字典']/parent::div/parent::div/parent::div//div[text()='1']/parent::div/parent::td").dblclick()

    #选择内行
    cscec.chooseIfPaper(page,False)
    time.sleep(0.2)
    s="//div[contains(text(),'收款账号')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tbody[2]/tr[1]/td[19]"
    page.locator(s).dblclick()
    time.sleep(0.2)
    cscec.getInputAbove(page,s).fill(d["摘要"]) #
    page.get_by_placeholder("事由不能超过").fill(d["事由"]) 
    page.locator("//span[text()='保存']/parent::div/parent::div/parent::div/parent::div").click()
    cscec.clickDigalog(page,"提示")
    cscec.getVisible(page,"//span[text()='提交']").click()
    cscec.clickDigalog(page,"处理意见")
    cscec.clickDigalog(page,"提示")
    page.locator("//button[@aria-label='remove']//span[@aria-label='close']//*[name()='svg']//*[name()='path' and contains(@d,'M563.8 512')]").click()

def main():
    db=excelDB()
    df=db.getDataframe("批量支付")
    page = browser.myBrowser("cscec").page
    for i,row in df.iterrows():
        if row["是否"]=="是":
            autoOne(page,row)
def queryData():
    db=excelDB()
    headers = ["序号","是否","组织机构","项目名称","分包类型","合同编号","项目简称","事由","摘要","付款金额","扣款金额","扣款事由","付款方式","内行收款编码或项目名称","附件"]
    db.queryData("批量支付",headers)
    db.close()

def writeData():
    db=excelDB()
    db.writeExcel("批量支付")
