import flet as ft
from src.Gui.FletGui2.views import ui_constants as C
from src.Gui.FletGui2.components.button import create_dropdown
import src.Gui.callProcess as callF

def create_auto_voucher(page:ft.Page):
    dropdown = create_dropdown(page, ["使用已打开浏览器","自动打开浏览器"], "自动打开浏览器")
    execute_button = ft.ElevatedButton(
        "自动制证",
        icon=ft.icons.PLAY_ARROW,
        on_click=lambda e: callF.thisProcess.run({"功能": "自动制证","参数":[dropdown.value]}),
        style=ft.ButtonStyle(
            color=ft.colors.WHITE,
            bgcolor=ft.colors.GREEN_500,
            padding=ft.padding.symmetric(horizontal=24, vertical=12),
            shape=ft.RoundedRectangleBorder(radius=6)
        ),
        width=240,
        height=44
    )
    return ft.Column(
        [
            ft.Text("自动制证", size=16, weight=ft.FontWeight.BOLD),
            ft.Row(
                [
                    dropdown,
                    execute_button,
                ],
                spacing=10,
                alignment=ft.MainAxisAlignment.CENTER,
            ),
        ],
        spacing=10,
    )