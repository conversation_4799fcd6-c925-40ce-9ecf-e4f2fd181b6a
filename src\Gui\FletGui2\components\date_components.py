import flet as ft
from datetime import datetime
import src.Gui.callProcess as callF
import src.base.cache as cache

def creatSAP_download(page:ft.Page):
    def update_start_date(e):
        if start_date_dialog.value:
            start_date.value = start_date_dialog.value.strftime("%Y-%m-%d")
            page.update()
    
    def update_end_date(e):
        if end_date_dialog.value:
            end_date.value = end_date_dialog.value.strftime("%Y-%m-%d")
            page.update()
    
    start_date_dialog = ft.DatePicker(
            on_change=update_start_date,
            first_date=datetime(2010, 1, 1),
            last_date=datetime(2030, 12, 31)
        )
        
    end_date_dialog = ft.DatePicker(
            on_change=update_end_date,
            first_date=datetime(2010, 1, 1),
            last_date=datetime(2030, 12, 31)
        )
    start_date = ft.TextField(
            value=cache.Lastdate,
            label="开始日期",
            width=220,
            height=50,
            read_only=True,
            border_color=ft.colors.BLUE_400,
            text_style=ft.TextStyle(font_family="Consolas", size=14),
            label_style=ft.TextStyle(color=ft.colors.BLUE_600, weight=ft.FontWeight.W_500, size=14),
            content_padding=ft.padding.only(left=12, right=12, top=8, bottom=8),
            border_radius=6,
            dense=True
        )
        
    end_date = ft.TextField(
            value=cache.Nowdate,
            label="结束日期",
            width=220,
            height=50,
            read_only=True,
            border_color=ft.colors.BLUE_400,
            text_style=ft.TextStyle(font_family="Consolas", size=14),
            label_style=ft.TextStyle(color=ft.colors.BLUE_600, weight=ft.FontWeight.W_500, size=14),
            content_padding=ft.padding.only(left=12, right=12, top=8, bottom=8),
            border_radius=6,
            dense=True
        )
        
    # Buttons with consistent styling
    button_style = ft.ButtonStyle(
            color=ft.colors.WHITE,
            bgcolor=ft.colors.BLUE_400,
            padding=ft.padding.symmetric(horizontal=16, vertical=10),
            shape=ft.RoundedRectangleBorder(radius=6)
        )
        
    start_date_btn = ft.ElevatedButton(
            "选择开始日期",
            icon=ft.icons.CALENDAR_TODAY,
            on_click=lambda _: start_date_dialog.pick_date(),
            style=button_style,
            height=40
        )
        
    end_date_btn = ft.ElevatedButton(
            "选择结束日期",
            icon=ft.icons.CALENDAR_TODAY,
            on_click=lambda _: end_date_dialog.pick_date(),
            style=button_style,
            height=40
        )
        
    execute_btn = ft.ElevatedButton(
            "执行导出",
            icon=ft.icons.PLAY_ARROW,
            on_click=lambda e: callF.thisProcess.run({"功能": "sap明细数据导出", "参数": [start_date.value, end_date.value]}),
            style=ft.ButtonStyle(
                color=ft.colors.WHITE,
                bgcolor=ft.colors.GREEN_500,
                padding=ft.padding.symmetric(horizontal=24, vertical=12),
                shape=ft.RoundedRectangleBorder(radius=6)
            ),
            width=220,
            height=44
        )
        
    return ft.Column(
        controls=[
            ft.Text("sap明细数据导出", size=20, weight=ft.FontWeight.W_500, color=ft.colors.BLACK),
            ft.Row(
                controls=[
                    start_date_dialog,
                    start_date,
                    start_date_btn,
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.CENTER,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
                height=60
            ),
            ft.Row(
                controls=[
                    end_date_dialog,
                    end_date,
                    end_date_btn,
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.CENTER,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
                height=60
            ),
            ft.Row(
                controls=[
                    execute_btn,
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.CENTER,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
                height=60
            ),
        ]
    )
        # Create input rows wit
def creatSAP_download_balance(page:ft.Page):
    def update_start_date(e):
        if start_date_dialog.value:
            start_date.value = start_date_dialog.value.strftime("%Y-%m-%d")
            page.update()
    
    def update_end_date(e):
        if end_date_dialog.value:
            end_date.value = end_date_dialog.value.strftime("%Y-%m-%d")
            page.update()
    
    start_date_dialog = ft.DatePicker(
            on_change=update_start_date,
            first_date=datetime(2010, 1, 1),
            last_date=datetime(2030, 12, 31)
        )
        
    end_date_dialog = ft.DatePicker(
            on_change=update_end_date,
            first_date=datetime(2010, 1, 1),
            last_date=datetime(2030, 12, 31)
        )
    start_date = ft.TextField(
            value=cache.theYearFirstDay,
            label="开始日期",
            width=220,
            height=50,
            read_only=True,
            border_color=ft.colors.BLUE_400,
            text_style=ft.TextStyle(font_family="Consolas", size=14),
            label_style=ft.TextStyle(color=ft.colors.BLUE_600, weight=ft.FontWeight.W_500, size=14),
            content_padding=ft.padding.only(left=12, right=12, top=8, bottom=8),
            border_radius=6,
            dense=True
        )
        
    end_date = ft.TextField(
            value=cache.Nowdate,
            label="结束日期",
            width=220,
            height=50,
            read_only=True,
            border_color=ft.colors.BLUE_400,
            text_style=ft.TextStyle(font_family="Consolas", size=14),
            label_style=ft.TextStyle(color=ft.colors.BLUE_600, weight=ft.FontWeight.W_500, size=14),
            content_padding=ft.padding.only(left=12, right=12, top=8, bottom=8),
            border_radius=6,
            dense=True
        )
    dropdown = ft.Dropdown(
        width=200,
        options=[
            ft.dropdown.Option("1", "往年科目余额表"),
            ft.dropdown.Option("2", "本年科目余额表"),
        ],
        value="2",  # 默认选择第一期
        border_radius=8,
        filled=True,
        text_style=ft.TextStyle(size=14, color=ft.colors.BLUE_ACCENT),
    )
    
    def dropdown_change(e):
        dropdown.value = e.control.value
        page.update()
    
    dropdown.on_change = dropdown_change
    # Buttons with consistent styling
    button_style = ft.ButtonStyle(
            color=ft.colors.WHITE,
            bgcolor=ft.colors.BLUE_400,
            padding=ft.padding.symmetric(horizontal=16, vertical=10),
            shape=ft.RoundedRectangleBorder(radius=6)
        )
        
    start_date_btn = ft.ElevatedButton(
            "选择开始日期",
            icon=ft.icons.CALENDAR_TODAY,
            on_click=lambda _: start_date_dialog.pick_date(),
            style=button_style,
            height=40
        )
        
    end_date_btn = ft.ElevatedButton(
            "选择结束日期",
            icon=ft.icons.CALENDAR_TODAY,
            on_click=lambda _: end_date_dialog.pick_date(),
            style=button_style,
            height=40
        )
        
    execute_btn = ft.ElevatedButton(
            "执行导出",
            icon=ft.icons.PLAY_ARROW,
            on_click=lambda e: callF.thisProcess.run({"功能": "科目余额表导出SAP", "参数": [start_date.value, end_date.value,dropdown.value]}),
            style=ft.ButtonStyle(
                color=ft.colors.WHITE,
                bgcolor=ft.colors.GREEN_500,
                padding=ft.padding.symmetric(horizontal=24, vertical=12),
                shape=ft.RoundedRectangleBorder(radius=6)
            ),
            width=220,
            height=44
        )
        
    return ft.Column(
        controls=[
            ft.Text("sap科目余额表导出", size=20, weight=ft.FontWeight.W_500, color=ft.colors.BLACK),
            ft.Row(
                controls=[
                    start_date_dialog,
                    start_date,
                    start_date_btn,
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.CENTER,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
                height=60
            ),
            ft.Row(
                controls=[
                    end_date_dialog,
                    end_date,
                    end_date_btn,
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.CENTER,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
                height=60
            ),
            ft.Row(
                controls=[
                    dropdown,
                    execute_btn,
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.CENTER,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
                height=60
            ),
        ]
    )
    
def creatDate_panel(page:ft.Page,title_name:str,start_date_default:str,end_date_default:str,button_text:str = "执行导出"):
    def update_start_date(e):
        if start_date_dialog.value:
            start_date.value = start_date_dialog.value.strftime("%Y-%m-%d")
            page.update()
    
    def update_end_date(e):
        if end_date_dialog.value:
            end_date.value = end_date_dialog.value.strftime("%Y-%m-%d")
            page.update()
    
    start_date_dialog = ft.DatePicker(
            on_change=update_start_date,
            first_date=datetime(2010, 1, 1),
            last_date=datetime(2030, 12, 31)
        )
        
    end_date_dialog = ft.DatePicker(
            on_change=update_end_date,
            first_date=datetime(2010, 1, 1),
            last_date=datetime(2030, 12, 31)
        )
    start_date = ft.TextField(
            value=start_date_default,
            label="开始日期",
            width=220,
            height=50,
            read_only=True,
            border_color=ft.colors.BLUE_400,
            text_style=ft.TextStyle(font_family="Consolas", size=14),
            label_style=ft.TextStyle(color=ft.colors.BLUE_600, weight=ft.FontWeight.W_500, size=14),
            content_padding=ft.padding.only(left=12, right=12, top=8, bottom=8),
            border_radius=6,
            dense=True
        )
        
    end_date = ft.TextField(
            value=end_date_default,
            label="结束日期",
            width=220,
            height=50,
            read_only=True,
            border_color=ft.colors.BLUE_400,
            text_style=ft.TextStyle(font_family="Consolas", size=14),
            label_style=ft.TextStyle(color=ft.colors.BLUE_600, weight=ft.FontWeight.W_500, size=14),
            content_padding=ft.padding.only(left=12, right=12, top=8, bottom=8),
            border_radius=6,
            dense=True
        )
        
    # Buttons with consistent styling
    button_style = ft.ButtonStyle(
            color=ft.colors.WHITE,
            bgcolor=ft.colors.BLUE_400,
            padding=ft.padding.symmetric(horizontal=16, vertical=10),
            shape=ft.RoundedRectangleBorder(radius=6)
        )
        
    start_date_btn = ft.ElevatedButton(
            "选择开始日期",
            icon=ft.icons.CALENDAR_TODAY,
            on_click=lambda _: start_date_dialog.pick_date(),
            style=button_style,
            height=40
        )
        
    end_date_btn = ft.ElevatedButton(
            "选择结束日期",
            icon=ft.icons.CALENDAR_TODAY,
            on_click=lambda _: end_date_dialog.pick_date(),
            style=button_style,
            height=40
        )
        
    execute_btn = ft.ElevatedButton(
            button_text,
            icon=ft.icons.PLAY_ARROW,
            on_click=lambda e: callF.thisProcess.run({"功能": button_text, "参数": [start_date.value, end_date.value]}),
            style=ft.ButtonStyle(
                color=ft.colors.WHITE,
                bgcolor=ft.colors.GREEN_500,
                padding=ft.padding.symmetric(horizontal=24, vertical=12),
                shape=ft.RoundedRectangleBorder(radius=6)
            ),
            width=220,
            height=44
        )
        
    return ft.Column(
        controls=[
            ft.Text(title_name, size=20, weight=ft.FontWeight.W_500, color=ft.colors.BLACK),
            ft.Row(
                controls=[
                    start_date_dialog,
                    start_date,
                    start_date_btn,
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.CENTER,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
                height=60
            ),
            ft.Row(
                controls=[
                    end_date_dialog,
                    end_date,
                    end_date_btn,
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.CENTER,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
                height=60
            ),
            ft.Row(
                controls=[
                    execute_btn,
                ],
                spacing=12,
                alignment=ft.MainAxisAlignment.CENTER,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
                height=60
            ),
        ]
    )       # Create input rows wit