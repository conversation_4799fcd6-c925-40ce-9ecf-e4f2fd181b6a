import duckdb

def get_table_columns(conn, table_name):
    """
    Get column information for a specific table.
    
    Args:
        conn: DuckDB connection object
        table_name (str): Name of the table
        
    Returns:
        list: List of tuples containing (column_name, data_type)
    """
    try:
        columns = conn.execute(f"PRAGMA table_info('{table_name}')").fetchall()
        return [(col[1], col[2]) for col in columns]  # (name, type)
    except Exception as e:
        print(f"Error getting columns for table {table_name}: {e}")
        return []

def list_duckdb_tables(database_path=':memory:'):
    """
    List all tables in a DuckDB database with their columns.
    
    Args:
        database_path (str): Path to the DuckDB database file. Defaults to in-memory database.
        
    Returns:
        dict: A dictionary where keys are table names and values are lists of (column_name, data_type) tuples
    """
    try:
        # Connect to the database
        conn = duckdb.connect(database_path)
        
        # Query to get all tables
        tables = conn.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'main' 
            ORDER BY table_name
        """).fetchall()
        
        # Get columns for each table
        result = {}
        for table in tables:
            table_name = table[0]
            columns = get_table_columns(conn, table_name)
            result[table_name] = columns
        
        return result
        
    except Exception as e:
        print(f"Error: {e}")
        return {}
    finally:
        # Ensure the connection is closed
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    database_path = r"D:\infoTech\fip-rpa-robot-oldscriptref\data\InternalData\Duckdb\example.duckdb"
    tables = list_duckdb_tables(database_path)
    
    if tables:
        print(f"Found {len(tables)} tables in the database:")
        print("=" * 50)
        
        for i, (table_name, columns) in enumerate(tables.items(), 1):
            print(f"{i}. 表名: {table_name}")
            if columns:
                print("   列名 (类型):")
                for col_name, col_type in columns:
                    print(f"   - {col_name} ({col_type})")
            else:
                print("   (No columns found or error reading table structure)")
            print("-" * 45)
    else:
        print("No tables found in the database or could not connect to the database.")