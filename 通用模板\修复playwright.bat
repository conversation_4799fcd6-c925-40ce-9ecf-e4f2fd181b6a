@echo off
color 0A
title 清除Command Processor自动运行设置

echo ==============================================
echo           Command Processor自动运行清理工具
echo ==============================================
echo.
echo 正在准备清理Command Processor的AutoRun设置...
echo.

:: 定义注册表路径变量
set "userPath=HKEY_CURRENT_USER\Software\Microsoft\Command Processor"
set "machinePath=HKEY_LOCAL_MACHINE\Software\Microsoft\Command Processor"

:: 处理当前用户路径
echo 正在处理当前用户配置: %userPath%
reg query "%userPath%" /v AutoRun >nul 2>nul
if %errorLevel% equ 0 (
    echo 找到AutoRun键值，正在删除...
    reg delete "%userPath%" /v AutoRun /f
    echo 当前用户路径下的AutoRun键值已删除
) else (
    echo 当前用户路径下未找到AutoRun键值
)
echo.

:: 处理本地机器路径（需要管理员权限）
echo 正在处理本地机器配置: %machinePath%
reg query "%machinePath%" /v AutoRun >nul 2>nul
if %errorLevel% equ 0 (
    echo 找到AutoRun键值，正在删除...
    reg delete "%machinePath%" /v AutoRun /f
    echo 本地机器路径下的AutoRun键值已删除
) else (
    echo 本地机器路径下未找到AutoRun键值
)
echo.

echo ==============================================
echo 操作完成！请关闭此窗口后重新启动命令行终端
echo ==============================================
echo.
pause