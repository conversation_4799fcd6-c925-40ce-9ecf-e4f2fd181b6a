import multiprocessing
from multiprocessing import managers, Queue
from io import StringIO
import traceback
import time
import threading
import sys
class myProcess():
    def __init__(self,inputFunction,managerMsg:Queue) -> None:
        self.inputFunction=inputFunction
        self.msg=managerMsg
        self.p=None
        self.sharedDict={"消息":"未开始"}
        self.start_time = None
        self.end_time = None
        self.processed_tables = 0
        self.processed_records = 0
    
    def _processRun_(self,paramsDict:dict):
        string_output = StringIO()
        # 读取子进程的stdout
        sys.stdout=string_output
        sig=1
        
        # 记录开始时间
        self.start_time = time.strftime("%Y-%m-%d %H:%M:%S")
        self.processed_tables = 0
        self.processed_records = 0
        
        def sys_output():
            while sig>0:
                s=string_output.getvalue()
                # 添加处理统计信息
                if self.start_time and self.end_time:
                    duration = time.time() - time.mktime(time.strptime(self.start_time, "%Y-%m-%d %H:%M:%S"))
                    stats = f"\n\n处理统计:\n开始时间: {self.start_time}\n结束时间: {self.end_time}\n处理时长: {duration:.2f}秒\n处理表数: {self.processed_tables}\n处理记录数: {self.processed_records}"
                    self.sharedDict["消息"]=s + stats
                else:
                    self.sharedDict["消息"]=s
                self.msg.put(self.sharedDict)
                time.sleep(0.1)
        thread1 = threading.Thread(target=sys_output)
        thread1.setDaemon(True)
        thread1.start()
        try:
            self.inputFunction(paramsDict)
            print("完成任务")
            sig=-1
            # 记录结束时间
            self.end_time = time.strftime("%Y-%m-%d %H:%M:%S")
            s=string_output.getvalue()
            self.sharedDict["消息"]=s
            self.msg.put(self.sharedDict)
        except Exception as e:
            tb = traceback.extract_tb(e.__traceback__)
            e1=""
            # 找到原始抛出异常的位置
            for frame in tb:  #多个异常的位置
                filename = frame.filename
                line_no = frame.lineno
                function_name = frame.name
                e1=e1+"\n"+f"File: {filename}, Line: {line_no}, Function: {function_name}"
            e1=str(e)+"\n"+e1
            s=string_output.getvalue()
            s2=s+"\n"+e1
            self.sharedDict["消息"]=s2
            self.msg.put(self.sharedDict)
            sig=-1
            time.sleep(0.1)
    def run(self,paramsDict:dict):
        if self.p is not None:
            self.p.terminate()
        self.p=multiprocessing.Process(target=self._processRun_,args=(paramsDict,))
        self.p.daemon=True
        self.p.start()
 
    def terminate(self):
            self.p.terminate()
            self.sharedDict["消息"]="已停止"
            self.msg.put(self.sharedDict)
    