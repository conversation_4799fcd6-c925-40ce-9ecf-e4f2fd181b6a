#!/usr/bin/env python3
"""
测试loading页面过渡效果的脚本
"""
import os
import sys
import time
import threading
import webview

# 添加项目路径
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(script_dir)

def simulate_server_startup():
    """模拟服务器启动过程"""
    print("模拟服务器启动...")
    time.sleep(3)  # 模拟启动时间
    print("服务器启动完成")

def load_test_page(window):
    """加载测试页面，模拟main_webview.py的行为"""
    # 模拟服务器启动
    simulate_server_startup()
    
    print("准备切换到测试页面...")
    time.sleep(1)
    
    # 使用JavaScript触发淡出动画
    window.evaluate_js("""
        if (window.triggerPageTransition) {
            console.log('触发页面切换动画');
            setTimeout(() => {
                alert('这里应该切换到主应用页面');
            }, 500);
        } else {
            alert('页面切换函数未找到');
        }
    """)

def main():
    """主函数"""
    # 获取loading.html的路径
    loading_html_path = os.path.join(script_dir, 'src', 'web', 'loading.html')
    
    if not os.path.exists(loading_html_path):
        print(f"错误：找不到loading.html文件: {loading_html_path}")
        return
    
    print(f"加载loading页面: {loading_html_path}")
    
    # 创建窗口
    window = webview.create_window(
        "测试 - 信小财加载页面",
        url=loading_html_path,
        width=1200,
        height=800
    )
    
    # 在后台线程中模拟页面切换
    threading.Thread(target=load_test_page, args=(window,), daemon=True).start()
    
    # 启动webview
    webview.start(debug=True)

if __name__ == "__main__":
    main()
