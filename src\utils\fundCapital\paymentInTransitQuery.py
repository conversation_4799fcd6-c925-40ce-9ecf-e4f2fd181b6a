
from playwright.sync_api import Playwright, sync_playwright,Page
import time
import src.utils.cscec as cscec
import src.utils.Browser.Browser as browser
import src.base.settings as settings
import threading
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd


columns_title=["项目名称","中台单据号","付款方式","支付单位","审批状态","流程节点名称","中台事由","供应商","合同名称","合同编号","内行收款方","单次金额","扣款金额","扣保证金金额","系统到期金额","单据比例","业务类型","提单日期","所属单位"]

def getPayState(page_thread:Page,lock:threading.Lock,dstate):
    cscec.reload(page_thread)
    cscec.toFunction(page_thread,"报账系统","财务操作中心","制证及支付")
    page_thread.locator("//div/div[text()='资金支付']").click()
    page_thread.locator("//label[text()='已提交支付']").click()
    for flowCode in dstate:
        with lock:
            if dstate[flowCode][0]=="已提交支付":
                nextif=True
                dstate[flowCode][0]="已提交支付更新状态"
            else:
                nextif=False
        if nextif:
            cscec.getVisible(page_thread,"//input[@placeholder='模糊查询:支持输入与扫描枪']").fill(f"{flowCode}")
            page_thread.get_by_label("制证及支付").get_by_text("查询").click()
            page_thread.locator(f"//td/div[text()='{flowCode}']").click()
            page_thread.locator("//span/button[text()='查看支付进度']").click()
            text=cscec.locatorDigalog(page_thread,"提示").text_content()
            if text:
                dstate[flowCode][1]=text[0:20]
            cscec.clickDigalog(page_thread,"提示")  

def getPayMsg(page_thread:Page,lock,year,dtitle,arr2:list,orgCode,dstate:dict):
    cscec.reload(page_thread)
    cscec.toFunction(page_thread,"报账系统","综合查询","单据明细查询(单位)")
    page_thread.locator("//label[contains(text(),'组织机构：')]/parent::div/following-sibling::div[1]//input").click()#点击输入
    page_thread.locator("//label[contains(text(),'组织机构：')]/parent::div/following-sibling::div[1]//input").fill(orgCode)
    page_thread.get_by_text("单据状态").click()
    page_thread.locator("//label[contains(text(),'业务日期')]/parent::div/following-sibling::div[1]//input").fill(f"{year}-01-01")
    page_thread.get_by_text("单据状态").click()
    page_thread.locator("//label[contains(text(),'业务日期')]/parent::div/following-sibling::div[3]//input").fill(f"{year}-12-31")
    page_thread.get_by_text("单据状态").click()
    page_thread.locator("//label[contains(text(),'业务类型：')]/parent::div/following-sibling::div[1]//input").click()#点击输入
    page_thread.locator("//label[contains(text(),'业务类型：')]/parent::div/following-sibling::div[1]//input").fill("10020002")

    page_thread.get_by_text("单据状态").click()  
    page_thread.get_by_text("制单中").click()
    page_thread.get_by_text("已结束").click()

    page_thread.locator("//img[contains(@src,'查询')]").click()
    page_thread.get_by_text("附件张数").click() #等待出现

    cscec.getVisible(page_thread,"//div[text()='每页']/following-sibling::input[1]").fill("900")
    cscec.getVisible(page_thread,"//div[text()='每页']/preceding-sibling::div[6]").click()
    time.sleep(5)

    table=cscec.getTr(page_thread,"单据状态")
    try:
        table_count=table.count()
        table.nth(0).locator("//td[2]/div").text_content()
    except:
        table_count=0

    for i in range(table_count):
        s2=table.nth(i).locator("//td[2]/div").text_content() #单据号，数字为列号码
        if len(s2)>3:

            ifNext1=False
            with lock:
               if dstate.get(s2,"不存在")=="不存在" :
                    ifNext2=True 
                    dstate[s2]=["结束",None,None]
               else :
                    ifNext2=False
                    if dstate[s2][0] =="结束":
                        ifNext1=True
                        dstate[s2][0]=="已更新"

            if ifNext1:
                s7=table.nth(i).locator("//td[7]/div").text_content() #事由
                s12=table.nth(i).locator("//td[14]/div").text_content() #状态
                s16=table.nth(i).locator("//td[18]/div").text_content() #流程节点名称
                dstate[s2]=[s12,s16,s7]
            if ifNext2:
                row=[0]*len(dtitle)
                s7=table.nth(i).locator("//td[7]/div").text_content() #事由
                s12=table.nth(i).locator("//td[14]/div").text_content() #状态
                s16=table.nth(i).locator("//td[18]/div").text_content() #流程节点名称
                s21=table.nth(i).locator("//td[21]/div").text_content() #所属单位

                with lock:
                    dstate[s2]=[s12,s16,s7]  #状态字典写入状态

                s5=table.nth(i).locator("//td[5]/div").text_content() #提单日期
                row[dtitle["中台单据号"]]=s2 #中台单据号
                row[dtitle["中台事由"]]=s7 #中台事由
                row[dtitle["审批状态"]]=s12 #中台状态
                row[dtitle["提单日期"]]=s5 #单据日期  
                row[dtitle["所属单位"]]=s21 #所属单位   
                row[dtitle["流程节点名称"]]=s16
                table.nth(i).locator("//td[2]").click()
                page_thread.locator("//label[contains(text(),'合同支付比例')]/parent::div/following-sibling::div[1]//input").click()#实际为等待页面加载
                row[dtitle["单据比例"]]=cscec.getVisible(page_thread,"//label[contains(text(),'合同支付比例')]/parent::div/following-sibling::div[1]//input").get_attribute("value")
                row[dtitle["项目名称"]]=cscec.getVisible(page_thread,"//label[contains(text(),'项目名称')]/parent::div/following-sibling::div[1]//input").get_attribute("value")
                row[dtitle["供应商"]]=cscec.getVisible(page_thread,"//label[contains(text(),'客')]/parent::div/following-sibling::div[1]//input").get_attribute("value")
                row[dtitle["合同名称"]]=cscec.getVisible(page_thread,"//label[contains(text(),'合同名称')]/parent::div/following-sibling::div[1]//input").get_attribute("value")
                row[dtitle["付款方式"]]=cscec.getVisible(page_thread,"//label[contains(text(),'支付方式')]/parent::div/following-sibling::div[1]//input").get_attribute("value")
                row[dtitle["支付单位"]]=cscec.getVisible(page_thread,"//label[contains(text(),'实际支付单位')]/parent::div/following-sibling::div[1]//input").get_attribute("value")
                cscec.getVisible(page_thread,"//label[contains(text(),'合同名称')]/parent::div/following-sibling::div[1]//input").click()
                row[dtitle["合同编号"]]=cscec.getVisible(page_thread,"//label[contains(text(),'合同名称')]/parent::div/following-sibling::div[1]//input").input_value()
                
                s6=cscec.getVisible(page_thread,"//div[contains(@style,'border-style: dashed;')]/div/div/div[2]/label").text_content()
                row[dtitle["业务类型"]]=s6
                if row[dtitle["付款方式"]]=="内行存款":
                    projectGetmoney=cscec.getTr(page_thread,"收款方项目").nth(0).locator("//td[5]/div").text_content()
                    row[dtitle["内行收款方"]]=projectGetmoney


                s="//div[contains(text(),'本次申请支付金额')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table/tbody[2]/tr[1]/td[7]/div"
                m0=page_thread.locator(s).text_content()
                row[dtitle["单次金额"]]=m0

                s="//div[contains(text(),'本次申请支付金额')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table/tbody[2]/tr[1]/td[6]/div"
                m0=page_thread.locator(s).text_content()
                row[dtitle["系统到期金额"]]=m0

                try:
                    s="//div[contains(text(),'扣款类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[6]/div"
                    m1=page_thread.locator(s).text_content(timeout=200)
                    row[dtitle["扣款金额"]]=m1
                except:
                    i

                try:
                    s="//div[contains(text(),'保证金类型')]/parent::span/parent::div/parent::td/parent::tr/parent::tbody/parent::table/parent::div/parent::div/following-sibling::div[1]//table//tr[1]/td[8]/div"
                    m2=page_thread.locator(s).text_content(timeout=200)
                    row[dtitle["扣保证金金额"]]=m2
                except:
                    i
                cscec.closeTab(page_thread)
                arr2.append(row)
    cscec.closeTab(page_thread)

def getMsg(cookies,cscecurl,lock:threading.Lock,dtitle,dstate,startYear,endYear,orgCodes,barrier:threading.Barrier,arr2:list,d_org_year:dict):
    playwright1 = sync_playwright().start()
    browser1 = playwright1.chromium.launch(channel="chrome",headless=False)
    newContext1 = browser1.new_context()
    page_thread=newContext1.new_page()
    newContext1.add_cookies(cookies)
    page_thread.goto(cscecurl)
    tryCount=5
    while tryCount>0:
        try:
            for year in range(startYear,endYear+1):
                for orgCode in orgCodes:
                    if d_org_year.get(orgCode+str(year),False):
                        continue    
                    print(year)
                    getPayMsg(page_thread,lock,year,dtitle,arr2,orgCode,dstate)
                    d_org_year[orgCode+str(year)]=True
            tryCount=-1
        except Exception as e:
            print(e)
            tryCount=tryCount-1
    #barrier.wait()
    #开始第二部分暂时取消
    #getPayState(page_thread,lock,dstate)
 

def main():   
    B=browser.myBrowser("cscec")
    page=B.page
    default_context=B.Context
    cookies = default_context.cookies()
    default_context.close()

    cscecurl=f"https://iam.cscec.com/cas/login?service=https%3A%2F%2Ffip.cscec.com%2FOSPPortal%2Fcallback"
    conn=excelDB()
    try:
        df=pd.read_sql(f"SELECT * FROM 在途付款单", conn.conn)
    except:
        df=pd.DataFrame(columns=columns_title)
        df.to_sql("在途付款单", conn.conn, if_exists="replace", index=False)
    arr=[df.columns.tolist()]+df.values.tolist()
    
    
    import datetime
    from src.utils.DB.configDB import configDB
    circularList=configDB().financialIntegrationUnits
    orgCodes=[circularList[i][0] for i in range(len(circularList))]

    dtitle={}
    dstate={}
    for i in range(len(arr[0])):
        dtitle[arr[0][i]]=i
    for i in range(1,len(arr)):
        dstate[arr[i][dtitle["中台单据号"]]]=["结束","结束",arr[i][dtitle["中台事由"]]]
    startYear=datetime.datetime.now().year-1
    endYear=datetime.datetime.now().year
    
    lock= threading.Lock()
    barrier = threading.Barrier(3)
    threads = []
    arr2=[]
    d_org_year={} #组织机构和年份的字典防冲突
    for i in range(3):
        print(i)
        t1 = threading.Thread(target=getMsg,args=(cookies,cscecurl,lock,dtitle,dstate,startYear,endYear,orgCodes,barrier,arr2,d_org_year))
        t1.setDaemon(True)
        t1.start()
        threads.append(t1) #线程保活，不然自动关闭啦
    print("持续运行中")
    for t in threads:
        t.join()
    if len(arr2)>0:
        conn.conn.executemany("INSERT INTO 在途付款单 VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",arr2)
        conn.conn.commit()
    arr3=conn.conn.execute("select 中台单据号 from 在途付款单").fetchall() 

    arr4=[]
    for i in range(0,len(arr3)): 
        row=[dstate[arr3[i][0]][0],dstate[arr3[i][0]][1],dstate[arr3[i][0]][2],arr3[i][0]]
        arr4.append(row)
    conn.conn.executemany("UPDATE 在途付款单 SET 审批状态 = ?,流程节点名称 = ?,中台事由 = ? WHERE 中台单据号 = ?",arr4)
    conn.conn.commit()
    conn.close()

def queryData():
    conn=excelDB()
    conn.queryData("在途付款单",columns=columns_title)
    conn.close()

def writeData():
    conn=excelDB()
    conn.writeExcel("在途付款单")
    conn.close()