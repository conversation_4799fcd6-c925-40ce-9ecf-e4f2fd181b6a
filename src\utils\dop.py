from playwright.sync_api import Playwright, sync_playwright,Page
import re
import src.utils.cscec as cscec
import pandas as pd
import numpy as numpy
import time
def goFund(page:Page,ifColse:bool):
    page.locator("//span[@title='资金管理']").click()
    page.locator("//td[normalize-space()='1']").click()
    if ifColse:
        page.locator("//div[text()='当前项目：']/following-sibling::div[1]/i").click() 

def augmentOne(page:Page): #注意新增的前置
    page.locator("//div[contains(text(),'金收支管理 / 资金支付申请 / 特殊支付申请')]/parent::div/following-sibling::div[2]//span[text()='新 增']").click()

def checkStr(s):
    if type(s)==float or type(s)==int or isinstance(s,numpy.int64) :
        return str(int(s))
    else:
        return s
        
def goFundSpecialPayment(page:Page):
    page.locator("//span[@title='资金收支管理']").click()
    page.locator("//a[@title='资金支付申请']/parent::li").click()
    cscec.getVisible(page,"//ul/li[1][text()='特殊支付申请']/following-sibling::li[1]").click()
    #page.locator("//ul/li[1][text()='特殊支付申请']").click()

def goFundContract(page:Page):
    page.locator("//span[@title='资金收支管理']").click()
    page.locator("//a[@title='债务管理台账']/parent::li").click()
    page.locator("//ul/li[contains(text(),'项目债务管理台账')]").click() #因为分供方合同维护左右有空格

def fundChooseProject(page:Page,project_code:str):
    page.locator("//div[text()='当前项目：']/following-sibling::div[1]/div[1]").click()
    project_code=checkStr(project_code)
    page.locator("//input[@placeholder='请输入项目编码']").fill(project_code)
    page.locator("//*[@id='portal-form-list-container']/div[3]/div/div/div[2]/div/div[2]/div[2]/div/div/div/div[1]/div[5]/button").click()
    page.locator("//td[normalize-space()='"+project_code+"']").click()


def pageCount(page:Page,count):
    page.locator("//div[@title='20 条/页']").click()
    page.locator(f"//li[text()='{count} 条/页']").click()

def getPageCount(page:Page):
    s=cscec.getVisible(page,"//li[@title='上一页']/preceding::li[contains(text(),'共')]").text_content()
    print(s)
    return int(re.findall("\d+",s)[0])
    

def getChoosePanel(page:Page):
    tyrCount=5
    while tyrCount>0:
        try:
            page.locator("//i[@class='icon aufontAll h-icon-all-filter-o']").click()
            if cscec.getVisible(page,"//div[@class='query-form']",timeout=30)!=None:
                tyrCount=-1
        except Exception as e:
            print(e)
            tyrCount=tyrCount-1
