@echo off
rem This script sets the environment variables for Ollama.

echo Setting Ollama environment variables...

rem Set the variables permanently for future sessions.
setx OLLAMA_MODELS "D:\infoTech\fipRPAruntime\ollama-windows-amd64"
setx OLLAMA_PATH "D:\infoTech\fipRPAruntime\ollama-windows-amd64"

rem Set the variables for the current session.
set OLLAMA_MODELS=D:\infoTech\fipRPAruntime\ollama-windows-amd64
set OLLAMA_PATH=D:\infoTech\fipRPAruntime\ollama-windows-amd64

echo.
echo Environment variables OLLAMA_MODELS and OLLAMA_PATH have been set.
echo Please restart any open terminals or applications for the changes to take effect.

pause
