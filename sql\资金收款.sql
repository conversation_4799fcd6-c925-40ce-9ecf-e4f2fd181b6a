select 
sum(case when a.内行客商 is not NULL and b.利润中心描述 is NULL  then a.内行或存款 else 0 end) as 货币收款,
sum(case when 文本 like '%抵房%' then 总收款金额 else 0 end) as 抵房收款,
sum(case when 文本 like '%商票%' or 文本 like '%银票%' then 总收款金额 else 0 end) as 票据收款,
sum(case when 文本 like '%代付%' then 总收款金额 else 0 end) as 代付收款,
sum(总收款金额) as 合计,
合计-货币收款-抵房收款-票据收款-代付收款 as 其他收款
from 收款台账 as a 
left join 主数据 as b on a.内行客商=b.利润中心描述
where a.过帐日期 between $1 and $2