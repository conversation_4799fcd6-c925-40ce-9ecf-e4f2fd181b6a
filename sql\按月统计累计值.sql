select * from (
SELECT 
    DATE_TRUNC('month', 过帐日期) AS month,        -- 提取月份
    round(SUM(SUM(case when 总账科目长文本 like '合同资产%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 累计合同资产,
    round(SUM(SUM(case when 总账科目长文本 like '%存款%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 存量,
    round(SUM(SUM(case when 总账科目长文本 like '应收%票据%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 应收票据,
    round(SUM(SUM(case when 总账科目长文本 like '应付%票据%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 应付票据,
    round(SUM(SUM(case when 总账科目长文本 like '%应付%保理%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 应付保理,
    round(SUM(SUM(case when 总账科目长文本 like '短期借款%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 借款,
    round(SUM(SUM(case when 总账科目长文本 like '其他应收款%待确认进项税%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 其他应收款待确认进项税,
    -round(SUM(SUM(case when 总账科目长文本 like '应收账款%待转销%' or 总账科目长文本 like '应交税费%待转销%' then 带符号的本位币金额 else 0 end)) OVER (ORDER BY DATE_TRUNC('month', 过帐日期))/10000,2) AS 应交税费待转销,
    存量+应收票据-应付保理-应付票据-借款 AS 净流量,
    CASE EXTRACT(MONTH FROM DATE_TRUNC('month', 过帐日期))
    WHEN 1 THEN '一月'
    WHEN 2 THEN '二月'
    WHEN 3 THEN '三月'
    WHEN 4 THEN '四月'
    WHEN 5 THEN '五月'
    WHEN 6 THEN '六月'
    WHEN 7 THEN '七月'
    WHEN 8 THEN '八月'
    WHEN 9 THEN '九月'
    WHEN 10 THEN '十月'
    WHEN 11 THEN '十一月'
    WHEN 12 THEN '十二月'
  END AS 中文月份
FROM 明细帐 
GROUP BY DATE_TRUNC('month', 过帐日期)) as t where t.month >= '2025-01-01'
ORDER BY month