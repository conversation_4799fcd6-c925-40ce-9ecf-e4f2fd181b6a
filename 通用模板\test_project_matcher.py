"""
项目名称模糊匹配功能测试
"""

import sys
import os
sys.path.append(".")

from src.utils.ai.project_matcher import ProjectMatcher, fuzzy_match_project
from src.utils.ai.ollama import match_project_name, get_project_suggestions_with_ai


def test_basic_matching():
    """测试基础匹配功能"""
    print("=== 测试基础匹配功能 ===")
    
    # 测试项目列表
    projects = [
        "中建三局第一建设工程有限责任公司",
        "中建二局第二建筑工程有限公司", 
        "中铁建设集团有限公司",
        "万科企业股份有限公司",
        "碧桂园控股有限公司",
        "恒大地产集团有限公司",
        "华润置地有限公司",
        "保利发展控股集团股份有限公司",
        "中国建筑股份有限公司",
        "中国中铁股份有限公司"
    ]
    
    # 测试查询
    test_queries = [
        "中建三局",
        "中建项目", 
        "万科地产",
        "建筑公司",
        "地产开发",
        "中铁建设"
    ]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        
        # 测试不同的匹配方法
        methods = ["edit", "jaccard", "cosine", "hybrid"]
        
        for method in methods:
            try:
                results = fuzzy_match_project(query, projects, top_k=3, method=method, threshold=0.1)
                print(f"  {method}方法:")
                for i, (project_info, similarity) in enumerate(results, 1):
                    print(f"    {i}. {project_info['text']} (相似度: {similarity:.3f})")
            except Exception as e:
                print(f"    {method}方法出错: {e}")


def test_project_matcher_class():
    """测试ProjectMatcher类"""
    print("\n=== 测试ProjectMatcher类 ===")
    
    # 创建项目匹配器
    matcher = ProjectMatcher(use_semantic=True)  # 不使用语义匹配以避免依赖问题
    
    # 项目列表（字典格式）
    projects = [
        {"id": "P001", "name": "中建三局第一建设工程有限责任公司", "code": "CSCEC3-1"},
        {"id": "P002", "name": "中建二局第二建筑工程有限公司", "code": "CSCEC2-2"},
        {"id": "P003", "name": "中铁建设集团有限公司", "code": "CRCC"},
        {"id": "P004", "name": "万科企业股份有限公司", "code": "VANKE"},
        {"id": "P005", "name": "碧桂园控股有限公司", "code": "BIGUIYUAN"}
    ]
    
    # 加载项目列表
    matcher.load_project_list(projects)
    
    # 测试查询
    query = "中建三局"
    print(f"查询: '{query}'")
    
    # 查找最佳匹配
    best_match = matcher.find_best_match(query, method="hybrid")
    if best_match:
        project_info, similarity = best_match
        print(f"最佳匹配: {project_info['original']['name']} (相似度: {similarity:.3f})")
    else:
        print("未找到匹配项目")
    
    # 查找多个匹配
    matches = matcher.find_best_matches(query, top_k=3, method="hybrid")
    print(f"前3个匹配:")
    for i, (project_info, similarity) in enumerate(matches, 1):
        original = project_info['original']
        print(f"  {i}. {original['name']} [{original['code']}] (相似度: {similarity:.3f})")


def test_integrated_functions():
    """测试集成的便捷函数"""
    print("\n=== 测试集成函数 ===")
    
    # 模拟项目数据（类似数据库查询结果）
    projects = [
        {"id": "001", "name": "北京CBD核心区建设项目", "code": "BJ-CBD-001"},
        {"id": "002", "name": "上海浦东新区住宅开发", "code": "SH-PD-002"}, 
        {"id": "003", "name": "深圳前海金融中心", "code": "SZ-QH-003"},
        {"id": "004", "name": "广州天河商业综合体", "code": "GZ-TH-004"},
        {"id": "005", "name": "成都高新区科技园", "code": "CD-GX-005"},
        {"id": "006", "name": "杭州西湖文化旅游项目", "code": "HZ-XH-006"},
        {"id": "007", "name": "武汉光谷软件园二期", "code": "WH-GG-007"},
        {"id": "008", "name": "南京江北新区基础设施", "code": "NJ-JB-008"}
    ]
    
    # 测试查询
    test_queries = [
        "北京CBD",
        "上海住宅",
        "深圳金融",
        "商业综合体",
        "科技园",
        "文化旅游"
    ]
    
    for query in test_queries:
        print(f"\n查询: '{query}'")
        
        # 使用集成的匹配函数
        try:
            results = match_project_name(query, projects, top_k=3, method="hybrid", threshold=0.1)
            print(f"匹配结果:")
            for i, result in enumerate(results, 1):
                original = result['original']
                print(f"  {i}. {original['name']} [{original['code']}] (相似度: {result['similarity']})")
        except Exception as e:
            print(f"匹配出错: {e}")


def test_performance():
    """测试性能"""
    print("\n=== 性能测试 ===")
    
    import time
    
    # 创建大量项目数据
    large_project_list = []
    for i in range(1000):
        large_project_list.append(f"项目{i:04d}-建设工程有限公司")
    
    # 添加一些特定项目
    large_project_list.extend([
        "中建三局第一建设工程有限责任公司",
        "中建二局第二建筑工程有限公司",
        "万科企业股份有限公司"
    ])
    
    query = "中建三局"
    
    # 测试不同方法的性能
    methods = ["edit", "jaccard", "cosine", "hybrid"]
    
    for method in methods:
        start_time = time.time()
        try:
            results = fuzzy_match_project(query, large_project_list, top_k=5, method=method)
            end_time = time.time()
            print(f"{method}方法: {end_time - start_time:.3f}秒, 找到{len(results)}个匹配")
        except Exception as e:
            print(f"{method}方法出错: {e}")


def main():
    """主测试函数"""
    print("开始测试项目名称模糊匹配功能...")
    
    try:
        test_basic_matching()
        test_project_matcher_class()
        test_integrated_functions()
        test_performance()
        
        print("\n=== 测试完成 ===")
        print("所有基础功能测试通过！")
        print("\n使用建议:")
        print("1. 对于简单匹配，使用 fuzzy_match_project() 函数")
        print("2. 对于复杂场景，使用 ProjectMatcher 类")
        print("3. 推荐使用 'hybrid' 方法获得最佳效果")
        print("4. 可以根据需要调整 threshold 参数过滤低相似度结果")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
