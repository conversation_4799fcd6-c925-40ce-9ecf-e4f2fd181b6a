import os
import sys

sys.path.append(".")
from playwright.sync_api import Playwright, sync_playwright,Page,Locator
import src.utils.cscec as cscec
import src.utils.Excel.excel as excel
import src.utils.dop as dop
import src.base.settings as settings
import re
import time
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_month_based_on_date() -> str:
    """
    根据当前日期判断返回月份
    如果当前日期在25号之后(不包括25号)，则返回下个月，否则返回当月
    返回格式为：xxxx年xx月
    """
    today = datetime.now()
    
    # 如果当前日期大于25号
    if today.day > 25:
        # 获取下个月的第一天
        if today.month == 12:
            next_month = today.replace(year=today.year + 1, month=1, day=1)
        else:
            next_month = today.replace(month=today.month + 1, day=1)
        month_str = next_month.strftime('%Y年%m月')
    else:
        month_str = today.strftime('%Y年%m月')
    
    return month_str


with sync_playwright() as playwright:
    browser = playwright.chromium.connect_over_cdp("http://localhost:9222")
    default_context = browser.contexts[0]
    page=cscec.switch_to_page(default_context,"中国建筑")
    page.locator("//span[contains(text(),'-')]/parent::div/parent::div/parent::td/div[1]").first.click()#起等待作用
    tdNext=page.locator("//span[contains(text(),'-')]/parent::div/parent::div/parent::td/div")
    loopCount=tdNext.count()
    print(loopCount)
    def clickOrg(tdNext:Locator):
        loopCount=tdNext.count()
        print(loopCount)
        for orgI in range(loopCount):
            if 'QxYMABAKUSJzQ00EBnAAAAAElFTkSuQmCC' in tdNext.nth(orgI).locator("xpath=/div[1]/img[3]").get_attribute("style"):
                tdNext.nth(orgI).locator("xpath=/div[1]/img[2]").click()
                time.sleep(0.5)
                tdNexts2=tdNext.nth(orgI).locator("xpath=/div[2]/div")
                clickOrg(tdNexts2)
    clickOrg(tdNext)
        
 



    

