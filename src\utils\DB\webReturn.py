import pandas as pd
import src.utils.DB.outputSQL as outputSQL
from src.utils.DB.mainDB import mainDB
from src.utils.DB.configDB import configDB
import src.base.settings as settings
import openpyxl

ws=openpyxl.load_workbook(settings.PATH_CONFIG+r"\配置文件-资金.xlsx",read_only=True)["单据分类"]
docCodeDict={}
for row in ws.iter_rows(min_row=2,values_only=True):
    docCodeDict[row[0]]=row[2]

def classification(row): #资金分类函数
    doc_code = str(row["中台单据号"])
    if len(doc_code) < 3:
        return "其他"  # 如果单据号太短，返回空字符串
    d=doc_code[0:3]
    lst=str(row["总账科目长文本"])
    if d in docCodeDict: 
        e=docCodeDict[d]
        if d == "CFK" and "劳务派遣" in lst:
            return "薪酬"
        elif d == "SRL" and "待资金系统" in lst:
            return "付款失败重付"
        elif d == "JFK":
            if "劳务" in lst:
                return "支付劳务款"
            elif "购货" in lst:
                return "支付购货款"
            elif "分包" in lst:
                return "支付分包款"
            else :
                return "支付供应商其他"
        else:
            return e
    else:
        return "其他"

def format_rmb(value):
    """
    将数字格式化为人民币格式，带千分位分隔符和两位小数
    
    Args:
        value: 要格式化的数字（可以是整数、浮点数或可转换为浮点数的字符串）
        
    Returns:
        str: 格式化后的人民币字符串（例如："¥1,234.56"）
    """
    try:
        # 转换为浮点数以处理各种输入类型
        num = float(value/10000)
        # 使用逗号作为千分位分隔符，保留2位小数
        return f"¥{num:,.2f}"+"万"
    except (ValueError, TypeError):
        # 如果转换失败，返回原值
        return value
def getFinancialAnalysisDataAll():
    benchmark=mainDB().conn.execute(outputSQL.指标计算).fetchall()[0]
    uninvoicedIncome=mainDB().conn.execute(outputSQL.开票计算).fetchall()[0][0]
    df=mainDB().conn.execute(outputSQL.按月统计时段值).df()
    df2=mainDB().conn.execute(outputSQL.按月统计累计值).df()
    d={"dashboardStats":{
    "totalIncome":format_rmb(benchmark[0]),
    "totalProfit":format_rmb(benchmark[1]),
    "totalFunds":format_rmb(benchmark[2]),
    "totalAssets":format_rmb(benchmark[3]),
    "totalLiabilities":format_rmb(-benchmark[4]),
    "uninvoicedIncome":format_rmb(uninvoicedIncome)},
    "incomeData": {"categories": ["累计收入", "当月收入"],"monthlyTrend": [df["累计收入"].tolist(), df["当月收入"].tolist()],"months": df["中文月份"].tolist()},
    "profitData": {"categories": ["累计利润", "当月利润"],"monthlyTrend": [df["累计利润"].tolist(), df["当月利润"].tolist()],"months": df["中文月份"].tolist()},
    "fundsData": {"categories": ["存量", "应收票据", "应付票据", "应付保理", "借款","净流量"],"monthlyTrend": [  df2["存量"].tolist(), df2["应收票据"].tolist(), df2["应付票据"].tolist(), df2["应付保理"].tolist(), df2["借款"].tolist(), df2["净流量"].tolist(), ],"months": df2["中文月份"].tolist()},
    "assetsData": {"categories": ["累计合同资产"],"monthlyTrend": [df2["累计合同资产"].tolist()],"months": df2["中文月份"].tolist()},
    "liabilitiesData": {"categories": ["应付分包款", "应付保理款", "应付材料款"],"values": [13000, 10000, 3000]},
    "taxData": {"categories": ["其他应收款待确认进项税", "应交税费待转销"],"monthlyTrend": [df2["其他应收款待确认进项税"].tolist(), df2["应交税费待转销"].tolist()],"months": df2["中文月份"].tolist()}} 
    return d

def getCashFlowData(data):
    import datetime
    startDate=datetime.datetime.strptime(data["start_date"], "%Y-%m-%d")
    endDate=datetime.datetime.strptime(data["end_date"], "%Y-%m-%d")
    from src.utils.DB.mainDB import mainDB
    concatArray=configDB().internalCustomers
    if len(concatArray)==0:
        replaceCondition1="('总部客商名称')"
    elif len(concatArray)==1:
        replaceCondition1=f"('{concatArray[0][0]}')"
    else:
        concatArrayTuple=tuple(concatArray[i][0] for i in range(1,len(concatArray)))
        replaceCondition1=str(concatArrayTuple)
    #replaceCondition2=configDB().expenseTransfer[0][0]
    replace2=outputSQL.资金存量变化
    replace2=replace2.replace("('总部客商名称')",replaceCondition1)
    df_fund=mainDB().conn.execute(replace2,(startDate,endDate)).df()
    df_fund=df_fund.fillna(0)

    df_receipt=mainDB().conn.execute(outputSQL.资金收款,(startDate,endDate)).df()
    df_receipt=df_receipt.fillna(0)

    df_payment=mainDB().conn.execute(outputSQL.资金付款分类,(startDate,endDate)).df()
    df_payment=df_payment.fillna(0)
    payment_sum=[df_payment.columns.tolist()]+df_payment.values.tolist()
    for i in range(len(payment_sum)):
        for j in range(len(payment_sum[i])):
            payment_sum[i][j]=format_rmb(payment_sum[i][j])

    cashFlow=mainDB().conn.execute(outputSQL.资金整理分类,(startDate,endDate)).fetchall()
    cashFlowList=[{"name":row[1],"value":row[0]} for row in cashFlow]

    otherPayment=mainDB().conn.execute(outputSQL.资金内行其他,(startDate,endDate)).df()
    otherPayment["分类"]=otherPayment.apply(classification,axis=1)
    otherPayment_sum=otherPayment.groupby("分类")["内行金额"].sum().reset_index()
    otherPayment_sum_List=[otherPayment_sum.columns.tolist()]+otherPayment_sum.values.tolist()
    for i in range(len(otherPayment_sum_List)):
        for j in range(len(otherPayment_sum_List[i])):
            otherPayment_sum_List[i][j]=format_rmb(otherPayment_sum_List[i][j])
    otherPayment=otherPayment.fillna(0)
    otherPaymentList=[otherPayment.columns.tolist()]+otherPayment.values.tolist()
    payment=mainDB().conn.execute("select * from 付款台账 where 过帐日期 between ? and ? ", (startDate,endDate)).df()
    payment=payment.fillna(0)
    paymentList=[payment.columns.tolist()]+payment.values.tolist()
    receipt=mainDB().conn.execute("select * from 收款台账 where 过帐日期 between ? and ? ", (startDate,endDate)).df()
    receipt=receipt.fillna(0)
    receiptList=[receipt.columns.tolist()]+receipt.values.tolist()



    return {
            "fund_status_data": [
                ["项目",  "可用存款", "应收票据","应付票据", "应付保理", "短期借款", "内部往来挂总部","净存量"],
                ["上期",  format_rmb(df_fund["原始存量"][0]), format_rmb(df_fund["应收票据"][0]), format_rmb(df_fund["应付票据"][0]), format_rmb(df_fund["应付保理"][0]), format_rmb(df_fund["短期借款"][0]), format_rmb(df_fund["内部往来挂总部"][0]), format_rmb(df_fund["净存量"][0])],
                ["当前", format_rmb(df_fund["原始存量2"][0]), format_rmb(df_fund["应收票据2"][0]), format_rmb(df_fund["应付票据2"][0]), format_rmb(df_fund["应付保理2"][0]), format_rmb(df_fund["短期借款2"][0]), format_rmb(df_fund["内部往来挂总部2"][0]), format_rmb(df_fund["净存量2"][0])],
                ["变化",  format_rmb(df_fund["原始存量2"][0]-df_fund["原始存量"][0]), format_rmb(df_fund["应收票据2"][0]-df_fund["应收票据"][0]), format_rmb(df_fund["应付票据2"][0]-df_fund["应付票据"][0]), format_rmb(df_fund["应付保理2"][0]-df_fund["应付保理"][0]), format_rmb(df_fund["短期借款2"][0]-df_fund["短期借款"][0]), format_rmb(df_fund["内部往来挂总部2"][0]-df_fund["内部往来挂总部"][0]), format_rmb(df_fund["净存量2"][0]-df_fund["净存量"][0])]
            ],
            
            "receipt_data": [
                ["收款类型", "总收款"],
                ["货币", format_rmb(df_receipt["货币收款"][0])],
                ["抵房", format_rmb(df_receipt["抵房收款"][0])],
                ["票据", format_rmb(df_receipt["票据收款"][0])],
                ["代付", format_rmb(df_receipt["代付收款"][0])],
                ["其他", format_rmb(df_receipt["其他收款"][0])],
                ["合计", format_rmb(df_receipt["合计"][0])]
            ],
            "payment_pivot_data": payment_sum,
            "other_payment_data":otherPayment_sum_List,
            "fund_distribution_data": cashFlowList,
            "receipt_details": receiptList,
            "payment_details": paymentList,
            "other_payment_details": otherPaymentList
        }