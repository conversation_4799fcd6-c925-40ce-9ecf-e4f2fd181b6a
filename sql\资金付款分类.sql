select 
(case when 供应商类型 like '%分包%' then '分包' when 供应商类型 like '%劳务%' then '劳务' when 供应商类型 like '%购货%' then '材料' when 供应商类型 like '%设备%' then '设备' else '其他' end) as 类型,
sum(总付款金额) as 总付款,
sum(case when a.内行客商 is not NULL and b.利润中心描述 is NULL  then a.内行或存款 else 0 end) as 货币付款,
sum(case when 文本 like '%代付%'  then a.本利润中心 else 0 end) as 本项目代付付款,
sum(case when a.内行客商 is not NULL and b.利润中心描述 is not NULL and 文本 like '%代付%'   then a.内行或存款 else 0 end) as 跨项目代付付款,
sum(case when a.内行客商 is not NULL and b.利润中心描述 is not NULL and 文本 like '%商票%'   then a.内行或存款 else 0 end) as 商票付款,
sum(case when a.内行客商 is not NULL and b.利润中心描述 is not NULL and 文本 like '%银票%'   then a.内行或存款 else 0 end) as 银票付款,
sum(扣履约保证金) as 扣保证金,
sum(供应链保理) as 保理,
sum(冲成本) as 冲减成本,
总付款 - 货币付款 - 本项目代付付款 - 跨项目代付付款 - 商票付款 - 银票付款 - 扣保证金 - 保理 - 冲减成本 as 其他
from 付款台账 as a 
left join 主数据 as b on a.内行客商 = b.利润中心描述
where a.过帐日期 between $1 and $2
group by 类型