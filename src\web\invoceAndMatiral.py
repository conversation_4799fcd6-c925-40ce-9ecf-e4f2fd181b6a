from pydantic import BaseModel
from typing import Dict, Any, List
from datetime import datetime
import src.base.settings as settings
from fastapi import HTTPException
from src.web.http import app  
from fastapi import Request
import logging
import traceback

@app.post("/api/invoice/template")
async def invoice_template(request: Request):
    try:
        data = await request.json()
        from src.utils.fipQuickFill.财务一体化发票自动录入 import getInvoiceData
        return getInvoiceData()
    except Exception as e:
        logging.error(f"Error in invoice-template: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=str(e))

