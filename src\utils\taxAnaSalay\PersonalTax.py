import pandas as pd
from decimal import Decimal, ROUND_HALF_UP
import numpy as np

def standard_round(num, decimals=0):
    """实现标准的四舍五入（当尾数为5时进位）"""
    # 确保 num 是字符串或 Decimal 对象，避免浮点数精度问题
    num_str = str(num)
    # 创建 Decimal 对象并设置舍入精度
    precision = Decimal('1.' + '0' * decimals if decimals > 0 else '1')
    # 执行标准四舍五入
    result = Decimal(num_str).quantize(precision, rounding=ROUND_HALF_UP)
    # 如果是整数，转换为 int 类型
    if decimals == 0 and result % 1 == 0:
        return int(result)
    return result


def calculateTax2(data):
    def calculate_tax(row):
        """
        计算累计预扣预缴个人所得税
        """
        # 计算累计应纳税所得额
        taxable_income = row['累计收入'] - row['累计扣除']  
        tax_rates = [
            (0, 36000, 0.03, 0),
            (36000, 144000, 0.1, 2520),
            (144000, 300000, 0.2, 16920),
            (300000, 420000, 0.25, 31920),
            (420000, 660000, 0.3, 52920),
            (660000, 960000, 0.35, 85920),
            (960000, float('inf'), 0.45, 181920)
        ]
        tax = 0
        for start, end, rate, quick_deduction in tax_rates:
            if taxable_income > start:
                taxable_part = min(taxable_income, end) - start
                tax += taxable_part * rate - quick_deduction
            else:
                break
        return tax
        return float(np.round(tax,np.round(tax,4),2))
    spdata=data["specialDeductionData"]
    taxdata=data["taxCalculationData"]
    #身份证号转字符
    dfspdata=pd.DataFrame(columns=spdata[0],data=spdata[1:])
    dftaxdata=pd.DataFrame(columns=taxdata[0],data=taxdata[1:])
    '''
    dftaxdata["基本养老保险费"].fillna(0,inplace=True)
    dftaxdata["住房公积金"].fillna(0,inplace=True)
    dftaxdata["基本医疗保险费"].fillna(0,inplace=True)
    dftaxdata["失业保险费"].fillna(0,inplace=True)
    dftaxdata["企业(职业)年金"].fillna(0,inplace=True)
    dftaxdata["其它扣款"].fillna(0,inplace=True)
    dftaxdata["调整收入"].fillna(0,inplace=True)
    dftaxdata["调整扣除"].fillna(0,inplace=True)
    dftaxdata["调整累计个税"].fillna(0,inplace=True)
    '''
    for col in ['本期收入','基本养老保险费', '住房公积金', '基本医疗保险费', '失业保险费', '企业(职业)年金','其它扣款','调整收入','调整扣除','调整累计个税']:
        dftaxdata[col] = pd.to_numeric(dftaxdata[col], errors='coerce')
    dftaxdata['身份证号'] = dftaxdata['身份证号'].apply(lambda x: str(x) if isinstance(x, int) else x)
    dfspdata['证件号码'] = dfspdata['证件号码'].apply(lambda x: str(x) if isinstance(x, int) else x)
    dftaxdata=dftaxdata.replace('', 0)
    dfspdata["合并专项附加扣除"]=dfspdata["累计子女教育"]+dfspdata["累计继续教育"]+dfspdata["累计赡养老人"]+dfspdata["累计3岁以下婴幼儿照护"]+dfspdata["累计住房贷款利息"]+dfspdata["累计住房租金"]+dfspdata["累计个人养老金"]
    dfspdata["月份"]=dfspdata["所得期间起"].apply(lambda x: x.split("-")[1].lstrip('0'))
    dftaxdata["月份"]=dftaxdata["月份"].apply(lambda x: x.split("-")[1].lstrip('0') if isinstance(x,str) and len(x.split("-"))>1 else x.split("-")[0] if isinstance(x,str) else str(x))
    dfspdata["身份证号"]=dfspdata["证件号码"]
    merged_df = dftaxdata.merge(dfspdata, on=['身份证号', '月份'], how='left')
    dftaxdata['累计专项附加'] = merged_df['合并专项附加扣除']
    dftaxdata['累计社保'] = dftaxdata.groupby(['身份证号', '算税地区'])['基本养老保险费'].cumsum()+dftaxdata.groupby(['身份证号', '算税地区'])['住房公积金'].cumsum()+dftaxdata.groupby(['身份证号', '算税地区'])['基本医疗保险费'].cumsum()+dftaxdata.groupby(['身份证号', '算税地区'])['失业保险费'].cumsum()+dftaxdata.groupby(['身份证号', '算税地区'])['企业(职业)年金'].cumsum() \
    +dftaxdata['基本养老保险费']+dftaxdata['住房公积金']+dftaxdata['基本医疗保险费']+dftaxdata['失业保险费']+dftaxdata['企业(职业)年金']
    dftaxdata['累计法定扣除'] = dftaxdata.groupby(['身份证号', '算税地区'])['月份'].transform(
    lambda x: pd.Series(range(1, len(x) + 1), index=x.index)
)*5000
    dftaxdata['累计收入']=dftaxdata.groupby(['身份证号', '算税地区'])['本期收入'].cumsum()- \
    dftaxdata.groupby(['身份证号', '算税地区'])['其它扣款'].cumsum()+ \
    dftaxdata.groupby(['身份证号', '算税地区'])['调整收入'].cumsum()+dftaxdata['本期收入']+dftaxdata['调整收入']+dftaxdata['其它扣款']
    dftaxdata['累计调整扣除']=dftaxdata.groupby(['身份证号', '算税地区'])['调整扣除'].cumsum()
    dftaxdata['累计扣除']=dftaxdata['累计法定扣除']+dftaxdata['累计专项附加']+dftaxdata['累计调整扣除']
    dftaxdata['累计应扣税款']=dftaxdata.apply(calculate_tax,axis=1)
    dftaxdata['累计上次税款'] = dftaxdata.groupby(['身份证号', '算税地区'])['累计应扣税款'].shift(1)
    # 计算差值：如果本期-上期>0，则取差值，否则取0
    dftaxdata['本次税款'] = dftaxdata.apply(lambda row: row['累计应扣税款'] - row['累计上次税款'] if row['累计应扣税款'] - row['累计上次税款'] > 0 else 0, axis=1)
    dftaxdata['本次税款']=dftaxdata['本次税款'].apply(lambda x: round(x, 2))
    dftaxdata=dftaxdata.fillna(0)
    return [dftaxdata.columns.tolist()]+dftaxdata.values.tolist()


def calculateTax(data):
    """
    计算累计预扣预缴个人所得税（纯数组循环实现）
    """
    def calculate_tax(taxable_income):
        """计算累计应纳税额"""
        tax_rates = [
            (0, 36000, 0.03, 0),
            (36000, 144000, 0.1, 2520),
            (144000, 300000, 0.2, 16920),
            (300000, 420000, 0.25, 31920),
            (420000, 660000, 0.3, 52920),
            (660000, 960000, 0.35, 85920),
            (960000, float('inf'), 0.45, 181920)
        ]
        tax = 0
        for start, end, rate, quick_deduction in tax_rates:
            if taxable_income > start:
                taxable_part = min(taxable_income, end) - start
                tax += taxable_part * rate
            else:
                break
        return standard_round(round(tax,5), 2)
    
    # 提取数据
    spdata = data["specialDeductionData"]
    taxdata = data["taxCalculationData"]
    
    # 删除标题为空的列
    if spdata and len(spdata) > 0:
        sp_header = spdata[0]
        # 找出非空标题的列索引
        valid_columns = [i for i, title in enumerate(sp_header) if title and str(title).strip()]
        # 筛选数据
        spdata = [[row[i] for i in valid_columns] for row in spdata]
    
    if taxdata and len(taxdata) > 0:
        tax_header = taxdata[0]
        # 找出非空标题的列索引
        valid_columns = [i for i, title in enumerate(tax_header) if title and str(title).strip()]
        # 筛选数据
        taxdata = [[row[i] for i in valid_columns] for row in taxdata]
    
    # 转换专项附加扣除数据
    sp_header = spdata[0]
    sp_rows = spdata[1:]
    
    # 构建身份证号+月份到专项附加扣除的映射
    sp_map = {}
    for row in sp_rows:
        # 确保数据完整
        if len(row) < len(sp_header):
            row += [0] * (len(sp_header) - len(row))
        
        row_dict = dict(zip(sp_header, row))
        
        # 处理证件号码和月份
        id_card = str(row_dict.get("证件号码", ""))
        period_start = row_dict.get("所得期间起", "")
        if isinstance(period_start, str) and "-" in period_start:
            month = period_start.split("-")[1].lstrip("0") or "0"
        if isinstance(period_start, str) and "/" in period_start:
            month = period_start.split("/")[1].lstrip("0") or "0"
        
        # 计算合并专项附加扣除
        deductions = sum(row_dict.get(key, 0) if isinstance(row_dict.get(key, 0), (int, float)) else 0 for key in [
            "累计子女教育", "累计继续教育", "累计赡养老人", 
            "累计3岁以下婴幼儿照护", "累计住房贷款利息", 
            "累计住房租金", "累计个人养老金"
        ])
        
        sp_map[(id_card, month)] = deductions
    
    # 转换个税计算数据
    tax_header = taxdata[0]
    tax_rows = taxdata[1:]
    
    # 结果数据
    # 定义新旧列名
    new_headers = [
        "累计专项附加", "累计社保", "累计法定扣除",
        "累计收入", "累计调整扣除", "累计扣除",
        "累计应扣税款", "累计上次税款", "本次税款"
    ]
    result_header = list(tax_header)
    for header in new_headers:
        if header not in result_header:
            result_header.append(header)

    result_rows = []
    
    # 跟踪每个人的累计数据
    person_tracking = {}
    
    # 处理每行数据
    for row in tax_rows:
        # 确保数据完整
        if len(row) < len(tax_header):
            row += [0] * (len(tax_header) - len(row))
        
        row_dict = dict(zip(tax_header, row))
        
        # 处理身份证号和月份
        id_card = str(row_dict.get("身份证号", ""))
        region = str(row_dict.get("算税地区", ""))
        month = row_dict.get("月份", "")
        
        if isinstance(month, str) and "-" in month:
            month = month.split("-")[1].lstrip("0") or "0"
        elif isinstance(month, str) and "/" in month:
            month = month.split("/")[1].lstrip("0") or "0"
        elif not isinstance(month, str):
            month = str(month)
        
        # 获取或初始化个人累计数据
        person_key = (id_card, region)
        if person_key not in person_tracking:
            person_tracking[person_key] = {
                "count": 0,
                "累计社保": 0,
                "累计收入": 0,
                "累计其它扣款": 0,
                "累计调整收入": 0,
                "累计调整扣除": 0,
                "累计应扣税款": 0,
                "累计上次税款": 0,
                "本次税款": 0,
            }
        
        person_data = person_tracking[person_key]
        #如果上一次月份不相等，则增加count

        if month != person_data.get("上期月份", ""):
            person_data["count"] += 1
        else:
            person_data["count"]
        person_data["上期月份"] = month
        # 获取社保数据
        pension = row_dict.get("基本养老保险费", 0) if row_dict.get("基本养老保险费", 0) is not None and isinstance(row_dict.get("基本养老保险费", 0), (int, float)) else 0
        housing_fund = row_dict.get("住房公积金", 0) if row_dict.get("住房公积金", 0) is not None and isinstance(row_dict.get("住房公积金", 0), (int, float)) else 0
        medical_insurance = row_dict.get("基本医疗保险费", 0) if row_dict.get("基本医疗保险费", 0) is not None and isinstance(row_dict.get("基本医疗保险费", 0), (int, float)) else 0
        unemployment_insurance = row_dict.get("失业保险费", 0) if row_dict.get("失业保险费", 0) is not None and isinstance(row_dict.get("失业保险费", 0), (int, float)) else 0
        annuity = row_dict.get("企业(职业)年金", 0) if row_dict.get("企业(职业)年金", 0) is not None and isinstance(row_dict.get("企业(职业)年金", 0), (int, float)) else 0
        
        # 获取收入和扣除数据
        current_income = row_dict.get("本期收入", 0) if row_dict.get("本期收入", 0) is not None and isinstance(row_dict.get("本期收入", 0), (int, float)) else 0
        other_deduction = row_dict.get("其它扣款", 0) if row_dict.get("其它扣款", 0) is not None and isinstance(row_dict.get("其它扣款", 0), (int, float)) else 0
        adjustment_income = row_dict.get("调整收入", 0) if row_dict.get("调整收入", 0) is not None and isinstance(row_dict.get("调整收入", 0), (int, float)) else 0
        adjustment_deduction = row_dict.get("调整扣除", 0) if row_dict.get("调整扣除", 0) is not None and isinstance(row_dict.get("调整扣除", 0), (int, float)) else 0
        adjustment_tax = row_dict.get("调整累计个税", 0) if row_dict.get("调整累计个税", 0) is not None and isinstance(row_dict.get("调整累计个税", 0), (int, float)) else 0
        
        # 计算各项累计值
        months_count = person_data["count"]
        
        # 专项附加扣除
        special_deduction = sp_map.get((id_card, month), 0) if sp_map.get((id_card, month), 0) is not None else 0
        
        # 累计社保
        current_social_security = pension + housing_fund + medical_insurance + unemployment_insurance + annuity
        accumulated_social_security = person_data["累计社保"] + current_social_security
        
        # 累计法定扣除
        accumulated_legal_deduction = months_count * 5000
        
        # 累计收入
        accumulated_income = person_data["累计收入"] + current_income + adjustment_income - other_deduction
        
        # 累计调整扣除
        accumulated_adjustment_deduction = person_data["累计调整扣除"] + adjustment_deduction
        
        # 累计扣除
        accumulated_deduction = accumulated_legal_deduction + special_deduction + accumulated_adjustment_deduction+accumulated_social_security
        
        # 累计应纳税所得额
        taxable_income = accumulated_income - accumulated_deduction
        
        # 计算累计应扣税款
        accumulated_tax = calculate_tax(taxable_income)
        
        # 计算本次税款
        last_tax = person_data["累计上次税款"]+person_data["本次税款"]+adjustment_tax
        current_tax = max(accumulated_tax - last_tax, 0)
        

        
        # 更新个人累计数据
        person_data["累计社保"] = accumulated_social_security
        person_data["累计收入"] = accumulated_income
        person_data["累计其它扣款"] += other_deduction
        person_data["累计调整收入"] += adjustment_income
        person_data["累计调整扣除"] = accumulated_adjustment_deduction
        person_data["累计应扣税款"] = accumulated_tax
        person_data["累计上次税款"] = last_tax
        person_data["本次税款"] = current_tax

        # 更新或添加结果行
        row_dict["累计专项附加"] = special_deduction
        row_dict["累计社保"] = accumulated_social_security
        row_dict["累计法定扣除"] = accumulated_legal_deduction
        row_dict["累计收入"] = accumulated_income
        row_dict["累计调整扣除"] = accumulated_adjustment_deduction
        row_dict["累计扣除"] = accumulated_deduction
        row_dict["累计应扣税款"] = accumulated_tax
        row_dict["累计上次税款"] = last_tax
        row_dict["本次税款"] = current_tax

        # 按最终表头顺序生成结果行
        result_row = [row_dict.get(h, 0) for h in result_header]
        result_rows.append(result_row)
    return [result_header] + result_rows