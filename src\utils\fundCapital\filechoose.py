import tkinter as tk
from tkinter import ttk
from tkinter import filedialog
import tkinter.ttk as ttk
import tkinter.messagebox  # 导入 messagebox 模块
import src.base.settings as settings
from src.utils.DB.midIntrSQLiteDB import excelDB
import pandas as pd
from src.utils.fileui import select_directory

def query(queryValue,queryList):
    for i in range(len(queryList)):
        if queryList[i] in queryValue:
            return i

class TemplateGeneratorApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("模板生成器")
        window_width = 600
        window_height = 250
        self.geometry(f"{window_width}x{window_height}")
        self.configure(bg="#f0f2f5")
        self.attributes('-topmost', True)
        self.iconbitmap(settings.CACHE_PATH+"/rpa.ico")  # 设置窗口图标
        screen_width = self.winfo_screenwidth()
        screen_height = self.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.resizable(False, False)

        # Style configuration for a modern look
        style = ttk.Style(self)
        style.theme_use('clam')  # Use clam for a cleaner look
        style.configure('TFrame', background='#f0f2f5')
        style.configure('TLabel', background='#f0f2f5', font=('Helvetica', 11))
        style.configure('TButton', font=('Helvetica', 10), padding=6)
        style.map('TButton', background=[('active', '#d9e4f5')])

        # Main container
        container = ttk.Frame(self, padding=(20, 20, 20, 20))
        container.pack(fill='both', expand=True)

        # 财务一体化支付单路径选择
        self.file1_var = tk.StringVar()
        row1 = ttk.Frame(container)
        row1.pack(fill='x', pady=5)
        ttk.Label(row1, text="财务一体化支付单:").pack(side='left', padx=(0,10))
        ttk.Entry(row1, textvariable=self.file1_var, width=50).pack(side='left')
        ttk.Button(row1, text="选择文件", command=self.select_file1).pack(side='left', padx=10)

        # 三局严选路径选择
        self.file2_var = tk.StringVar()
        row2 = ttk.Frame(container)
        row2.pack(fill='x', pady=5)
        ttk.Label(row2, text="三局严选:").pack(side='left', padx=(0,10))
        ttk.Entry(row2, textvariable=self.file2_var, width=50).pack(side='left')
        ttk.Button(row2, text="选择文件", command=self.select_file2).pack(side='left', padx=10)

        # 按钮行
        button_row = ttk.Frame(container)
        button_row.pack(fill='x', pady=(20,0))
        ttk.Button(button_row, text="根据文件匹配自动生成模板", command=self.generate_template_auto).pack(side='left', expand=True)
        ttk.Button(button_row, text="直接生成模板", command=self.generate_template_direct).pack(side='left', expand=True, padx=20)

    def select_file1(self):
        path = filedialog.askopenfilename(title="选择财务一体化支付单")
        if path:
            self.file1_var.set(path)

    def select_file2(self):
        path = filedialog.askopenfilename(title="选择三局严选文件")
        if path:
            self.file2_var.set(path)

    def generate_template_auto(self):
        file1 = self.file1_var.get()
        file2 = self.file2_var.get()
        # TODO: 在这里添加根据文件匹配自动生成模板的逻辑
        df1=pd.read_excel(file1)
        #修改列名
        df1 = df1.rename(columns={'申请单号': '一体化申请单号'})

        df2=pd.read_excel(file2,dtype={"支付单号":str,"关联结算单号":str})
        df2["索引"] = [str(i) for i in range(len(df2))]
        df1["索引"]=""
        queryList=df2["支付单名称"].tolist()
        for i in range(len(df1)):
            value=str(df1.loc[i,"付款摘要"])
            queryindex=query(value,queryList)
            df1.loc[i,"索引"]=str(queryindex)
        df3=pd.merge(df1,df2,on="索引",how="left")
        df3["核心企业"]="中建三局集团有限公司"
        df3["备注"]="需要补充"
        df3['授信单位']='中建三局集团有限公司-本部'
        df3["合同名称"]="需要补充"
        df3["合同编号"]="需要补充"
        df3["我方增信方式"]="需要填"
        df3["保理合同利率"]="需要补充"
        df3["授信合同"]="需要补充"
        df3["附件"]="需要补充"
        path=select_directory()
        df3.to_excel(path+"/反向保理申请.xlsx",index=False)


        tkinter.messagebox.showinfo("提示", f"已根据文件自动生成模板:\n{file1}\n{file2}")

    def generate_template_direct(self):
        headers = ["序号","支付单号","一体化申请单号","资金方","融资成本(元)","账期起始日期","账期到期日期","保理合同利率","授信合同","支付金额(元)","我方增信方式","资金提供机构","备注","合同名称","合同编号","授信单位","备注","核心企业","附件"]
        df=pd.DataFrame(columns=headers)
        df.to_excel("反向保理申请.xlsx",index=False)
        tkinter.messagebox.showinfo("提示", "已直接生成模板")


