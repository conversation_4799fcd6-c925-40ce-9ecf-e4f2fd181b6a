# 报表助手
import flet as ft
from src.Gui.FletGui2.views import ui_constants as C
from src.Gui.FletGui2.components.date_range_components import DateRangeComponent
from src.Gui.FletGui2.components.button import create_execute_button_with_text_row1
from src.Gui.FletGui2.components.button import JiuQiReport,FinancialReportView

def create_test_case_tab1(page: ft.Page):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[
                    
                    create_execute_button_with_text_row1(page,"批量生成项目报表","生成本地excel表","生成项目报表"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )
def create_test_case_tab2(page: ft.Page):
        """Helper function to create a tab with a DateRangeComponent."""
        return ft.Container(
            content=ft.Column(
                controls=[
                    JiuQiReport(),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    FinancialReportView(),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_text_row1(page,"坏账分析","先再sap坏账计提界面进行excel导出，然后上面选一下上年度报表路径，执行坏账分析,选择导出的excel,再选择保存位置","执行坏账分析"),
                    
                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )

def get_view(page: ft.Page, view_display_name: str, show_home_callback):


    def _handle_return_click(e):
        show_home_callback()

    # Header section
    header = ft.Container(
        content=ft.Row(
            [
                ft.Text(
                    f"{view_display_name}模块",
                    size=24, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON

                ),
                ft.Container(),  # 占位符用于左右分隔
                ft.ElevatedButton(
                    "返回主菜单",
                    icon=ft.icons.ARROW_BACK_IOS_NEW_ROUNDED,
                    on_click=_handle_return_click,
                    style=ft.ButtonStyle(
                        color=ft.colors.WHITE,
                        bgcolor=C.ACCENT_COLOR,
                        padding=ft.padding.symmetric(horizontal=24, vertical=12),
                        shape=ft.RoundedRectangleBorder(radius=8),
                        elevation=3,
                        shadow_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
                    ),
                    height=48
                ),
            ],
            spacing=8,
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            vertical_alignment=ft.CrossAxisAlignment.CENTER
        ),
        padding=ft.padding.only(bottom=20),
        alignment=ft.alignment.center
    )

    # Create tab content for each test case
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="项目报表",
                icon=ft.icons.PAYMENT,
                content=create_test_case_tab1(page)
            ),
                        ft.Tab(
                text="报表助手",
                icon=ft.icons.COMPUTER_OUTLINED,
                content=create_test_case_tab2(page)
            )
        ],
        expand=True,
        tab_alignment=ft.TabAlignment.CENTER,
        label_color=C.ACCENT_COLOR,
        unselected_label_color=ft.colors.with_opacity(0.7, C.TEXT_COLOR),
        indicator_color=C.ACCENT_COLOR,
        indicator_tab_size=True,
        label_padding=ft.padding.symmetric(horizontal=20, vertical=12),
        # Enhanced tab styling
        overlay_color=ft.colors.with_opacity(0.1, C.ACCENT_COLOR),
        divider_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
        indicator_border_radius=4,
        indicator_padding=ft.padding.symmetric(horizontal=8),
    )

    # Create tabs with improved styling
    
    scrollable_content = ft.Container(
        content=ft.Column(
            [
                header,
                # Tabs container with proper expansion
                ft.Container(
                    content=tabs,
                    expand=True,
                    margin=ft.margin.symmetric(vertical=10),
                )
            ],
            spacing=0,
            expand=True,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
        ),
        expand=True,
        padding=ft.padding.symmetric(horizontal=15, vertical=10)
    )
    
    # Main container with proper constraints
    return ft.Container(
        content=scrollable_content,
        expand=True,
        padding=ft.padding.only(top=10, bottom=10),
        margin=ft.margin.all(0)
    )
