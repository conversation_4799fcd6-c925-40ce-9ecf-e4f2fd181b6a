import os
import re
import pandas as pd
import pdfplumber

def extract_by_regex(text,data:list):
    pattern = r"名称：(.*?)\n"
    match = re.search(pattern, text)
    if match:
        result = match.group(1)
        data[1]=result
    pattern = r'统一社会信用代码/纳税人识别号：([A-Z0-9]{18})'
    match = re.search(pattern, text)
    if match:
        result = match.group(1)
        data[2]=result

def extract_from_path(path):
    data=[path,"","",""]
    with pdfplumber.open(path) as pdf:
        for page in pdf.pages:
            tables = page.extract_tables()
            for table in tables:
                for row in table:
                    #print(row)
                    for index,cell in enumerate(row):
                        if cell=='销\n售\n方\n信\n息':
                            extract_by_regex(row[index+1],data)
                        if cell=='备\n注':
                            data[3]=data[3]+row[index+1].strip().replace('\n','')
    return data
    



 
 
directory_path = r"C:\Users\<USER>\Desktop\发票识别"
 
if __name__ == '__main__':
    # 遍历目录下的所有文件
    data= []
    for filename in os.listdir(directory_path):
        if filename.endswith('.pdf'):
            # 构建文件的完整路径
            filename = os.path.join(directory_path, filename)
            row=extract_from_path(filename)
            data.append(row)
    print(data)