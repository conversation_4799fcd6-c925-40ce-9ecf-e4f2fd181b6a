# 数据同步-统计大师
import flet as ft
from src.Gui.FletGui2.views import ui_constants as C
from src.Gui.FletGui2.components.date_range_components import DateRangeComponent
from src.Gui.FletGui2.components.date_components import creatSAP_download,creatSAP_download_balance,creatDate_panel
from src.Gui.FletGui2.components.button import create_execute_button_with_row, create_execute_button_with_text_row1,create_execute_button_with_text_row2
from src.Gui.FletGui2.components.button import create_execute_button_with_data_maintain
import src.base.cache as cache
def create_tab1(page: ft.Page):
        return ft.Container(
            content=ft.Column(
                controls=[
                    create_execute_button_with_text_row1(page, "sap主数据导出", "导出主数据用于内外部客商及利润中心组识别", "导出主数据"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    creatSAP_download(page),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    creatSAP_download_balance(page),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_text_row1(page, "sap内部对账导出", "导出内部对账用于汇总分析", "导出内部对账"),
                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )
def create_tab2(page: ft.Page): #一体化台账
        return ft.Container(
            content=ft.Column(
                controls=[
                    create_execute_button_with_text_row1(page, "导出一体化合同台账", "导出一体化合同台账", "导出一体化合同台账"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_text_row2(page, "导出一体化预付款及保证金台账", "导出一体化预付款及保证金台账", "失败继续","重新开始",["导出一体化预付款及保证金台账失败继续","导出一体化预付款及保证金台账重新开始"]),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    creatDate_panel(page,"一体化存量余额导出",cache.theMonthFirstDay,cache.Nowdate,"一体化存量余额导出"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_row(page, "一体化支付单导出", "查询在途单据数据库", "更新在途单据数据库", "同步一体化在途单据"),
                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )
def create_tab3(page: ft.Page): #一体化资金
        return ft.Container(
            content=ft.Column(
                controls=[
                    creatDate_panel(page,"一体化存量余额导出",cache.theMonthFirstDay,cache.Nowdate,"一体化存量余额导出"),
                    ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                    create_execute_button_with_row(page, "一体化支付单导出", "查询在途单据数据库", "更新在途单据数据库", "同步一体化在途单据"),
                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )
def create_tab4(page: ft.Page): #数据库维护
        return ft.Container(
            content=ft.Column(
                controls=[create_execute_button_with_data_maintain(page),
                ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                create_execute_button_with_text_row1(page, "重建数据结构", "用于数据库维护", "重建数据结构"),
                ft.Divider(height=1, color=ft.colors.with_opacity(0.2, ft.colors.PRIMARY)),
                create_execute_button_with_text_row2(page, "重新插入数据库", "用于数据库维护前面出错", "打开配置文件","重新插入数据库"),

                ],
                expand=True,
                scroll=ft.ScrollMode.AUTO,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                alignment=ft.MainAxisAlignment.START,
                spacing=20,
            ),
            padding=ft.padding.symmetric(vertical=20, horizontal=30),
            alignment=ft.alignment.top_center,
            expand=True,
            bgcolor=ft.colors.with_opacity(0.02, C.ACCENT_COLOR),
            border_radius=8,
            margin=ft.margin.all(5),
        )



def get_view(page: ft.Page, view_display_name: str, show_home_callback):
    """Returns the content for the Data Scanner view with tabbed interface."""

    def _handle_return_click(e):
        show_home_callback()

    # Header section
    header = ft.Container(
        content=ft.Row(
            [
                ft.Text(
                    f"{view_display_name}模块",
                    size=24, weight=ft.FontWeight.BOLD, color=C.ACCENT_COLOR, font_family=C.FONT_ORBITRON

                ),
                ft.Container(),  # 占位符用于左右分隔
                ft.ElevatedButton(
                    "返回主菜单",
                    icon=ft.icons.ARROW_BACK_IOS_NEW_ROUNDED,
                    on_click=_handle_return_click,
                    style=ft.ButtonStyle(
                        color=ft.colors.WHITE,
                        bgcolor=C.ACCENT_COLOR,
                        padding=ft.padding.symmetric(horizontal=24, vertical=12),
                        shape=ft.RoundedRectangleBorder(radius=8),
                        elevation=3,
                        shadow_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
                    ),
                    height=48
                ),
            ],
            spacing=8,
            alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
            vertical_alignment=ft.CrossAxisAlignment.CENTER
        ),
        padding=ft.padding.only(bottom=20),
        alignment=ft.alignment.center
    )

    # Create tab content for each test case

    # Create tabs with improved styling
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="sap数据导出",
                icon=ft.icons.COMPASS_CALIBRATION,
                content=create_tab1(page)
            ),
            ft.Tab(
                text="财务一体化导出",
                icon=ft.icons.COMPASS_CALIBRATION,
                content=create_tab2(page)
            ),
            #ft.Tab(
            #    text="财务一体化资金下载",
            #    icon=ft.icons.COMPASS_CALIBRATION,
            #    content=create_tab3(page)
            #),
            ft.Tab(
                text="数据库维护",
                icon=ft.icons.CONSTRUCTION_ROUNDED,
                content=create_tab4(page)
            )
        ],
        expand=True,
        tab_alignment=ft.TabAlignment.CENTER,
        label_color=C.ACCENT_COLOR,
        unselected_label_color=ft.colors.with_opacity(0.7, C.TEXT_COLOR),
        indicator_color=C.ACCENT_COLOR,
        indicator_tab_size=True,
        label_padding=ft.padding.symmetric(horizontal=20, vertical=12),
        # Enhanced tab styling
        overlay_color=ft.colors.with_opacity(0.1, C.ACCENT_COLOR),
        divider_color=ft.colors.with_opacity(0.3, C.ACCENT_COLOR),
        indicator_border_radius=4,
        indicator_padding=ft.padding.symmetric(horizontal=8),
    )

    # The 'tabs' control is now directly included in the Column below,
    # replacing the 'tabs_container'.

    # Create a scrollable content area with improved layout
    scrollable_content = ft.Container(
        content=ft.Column(
            [
                header,
                # Tabs container with proper expansion
                ft.Container(
                    content=tabs,
                    expand=True,
                    margin=ft.margin.symmetric(vertical=10),
                )
            ],
            spacing=0,
            expand=True,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER
        ),
        expand=True,
        padding=ft.padding.symmetric(horizontal=15, vertical=10)
    )
    
    # Main container with proper constraints
    return ft.Container(
        content=scrollable_content,
        expand=True,
        padding=ft.padding.only(top=10, bottom=10),
        margin=ft.margin.all(0)
    )
