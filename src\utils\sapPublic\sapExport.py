import os
import src.utils.sapPublic.GetSAPSession as GetSAPSession
import time
import win32com.client
import pythoncom
import os
import src.utils.Excel.excel as excel
import threading
from datetime import datetime, timedelta
import src.base.settings as settings
import duckdb
from src.utils.DB.configDB import configDB


#大致模板
def exportDetails(session,companyCode,profitCenterGroup,startDate,endDate,path,pathList:list,onlyPayable=False):
    session.StartTransaction( "ZGL0008")
    session.findById("wnd[0]/usr/ctxtS_BUKRS-LOW").Text = companyCode#公司
    session.findById("wnd[0]/usr/ctxtP_PCGRP").Text = profitCenterGroup #利润中心
    session.findById("wnd[0]/usr/ctxtS_CPUDT-LOW").Text = startDate
    session.findById("wnd[0]/usr/ctxtS_CPUDT-HIGH").Text = endDate
    if onlyPayable:
        session.findById("wnd[0]/usr/ctxtS_HKONT-LOW").text = "2202*"
    session.findById("wnd[0]/usr/ctxtS_CPUDT-HIGH").SetFocus()
    session.findById("wnd[0]/usr/ctxtS_CPUDT-HIGH").caretPosition = 8
    session.findById("wnd[0]/tbar[1]/btn[8]").press()
    try:
        session.findById("wnd[0]/usr/btn%PF01003_0100").press()
        session.findById("wnd[0]/tbar[1]/btn[8]").press()
        session.findById("wnd[0]/mbar/menu[0]/menu[3]/menu[2]").Select()
        #session.findById("wnd[1]/usr/subSUBSCREEN_STEPLOOP:SAPLSPO5:0150/sub:SAPLSPO5:0150/radSPOPLI-SELFLAG[3,0]").select() #加上改行，选择html导出
        session.findById("wnd[1]/tbar[0]/btn[0]").press()
        filePath = f"{path}\导出明细数据\{profitCenterGroup}从{startDate}到{endDate}.txt"
        if os.path.exists(filePath):  # 判断文件是否存在
            try:
                os.remove(filePath)  # 尝试删除文件
            except Exception as e:
                print(f"删除文件时发生错误: {e}")
        if not os.path.exists(f"{path}/导出明细数据"):
            os.makedirs(f"{path}/导出明细数据")
        session.findById("wnd[1]/usr/ctxtDY_PATH").Text = f"{path}/导出明细数据"
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").Text = f"{profitCenterGroup}从{startDate}到{endDate}.txt"
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").caretPosition = 7
        session.findById("wnd[1]/tbar[0]/btn[0]").press()
        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        pathList.append(filePath)
        return True
    except Exception as e:
        print("未找到数据")
        return False
def exportBalanceTable(session,companyCode,profitCenterGroup,startDate,endDate,path,pathList:list):
    try:
        session.StartTransaction( "ZGL0004")
        session.findById("wnd[0]/usr/chkP_CLEAR").selected = True
        session.findById("wnd[0]/usr/ctxtS_BUKRS-LOW").text = companyCode #公司
        session.findById("wnd[0]/usr/ctxtS_ZYWBK-LOW").text = "02"
        session.findById("wnd[0]/usr/chkP_CLEAR").setFocus()
        session.findById("wnd[0]/tbar[1]/btn[8]").press()
        session.findById("wnd[1]/usr/chkP_GRPPC").selected = True
        session.findById("wnd[1]/usr/chkP_PRCTR").selected = True
        session.findById("wnd[1]/usr/chkP_POSID").selected = True
        session.findById("wnd[1]/usr/chkP_KUNNR").selected = True
        session.findById("wnd[1]/usr/chkP_LIFNR").selected = True
        session.findById("wnd[1]/usr/chkP_ZZ010").selected = True
        session.findById("wnd[1]/usr/chkP_ZZ011").selected = True
        session.findById("wnd[1]/usr/chkP_ZZ017").selected = True
        session.findById("wnd[1]/usr/chkP_MWSKZ").selected = True
        session.findById("wnd[1]/usr/ctxtS_BUDAT-LOW").text = startDate
        session.findById("wnd[1]/usr/ctxtS_BUDAT-HIGH").text =endDate
        session.findById("wnd[1]/usr/ctxtP_PCGRP").text = profitCenterGroup #利润中心
        session.findById("wnd[1]/usr/chkP_MWSKZ").setFocus()
        session.findById("wnd[1]/tbar[0]/btn[8]").press()
        session.findById("wnd[0]/mbar/menu[0]/menu[1]/menu[2]").select()
        session.findById("wnd[1]/tbar[0]/btn[0]").press()
        filePath = f"{path}\导出科目余额表\{profitCenterGroup}.txt"
        if os.path.exists(filePath):  # 判断文件是否存在
            try:
                os.remove(filePath)  # 尝试删除文件
            except Exception as e:
                print(f"删除文件时发生错误: {e}")
        if not os.path.exists(f"{path}/导出科目余额表"):
            os.makedirs(f"{path}/导出科目余额表")
        session.findById("wnd[1]/usr/ctxtDY_PATH").text =f"{path}/导出科目余额表"
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = f"{profitCenterGroup}.txt"
        session.findById("wnd[1]/usr/ctxtDY_FILENAME").caretPosition = 10
        session.findById("wnd[1]/tbar[0]/btn[0]").press()
        pathList.append(filePath)
    except:
        print("未找到数据")
def exportBaseData(session,companyCode,profitCenterGroup,path,pathList:list):
    session.StartTransaction("ZMD0005")
    session.findById("wnd[0]/usr/ctxtS_BUKRS-LOW").text = companyCode
    session.findById("wnd[0]/usr/ctxtS_KHINR-LOW").text = profitCenterGroup
    session.findById("wnd[0]/usr/ctxtS_KHINR-LOW").setFocus()
    session.findById("wnd[0]/usr/ctxtS_KHINR-LOW").caretPosition = 10
    session.findById("wnd[0]/tbar[1]/btn[8]").press()
    session.findById("wnd[0]/mbar/menu[0]/menu[1]/menu[2]").select()
    session.findById("wnd[1]/tbar[0]/btn[0]").press()

    filePath = f"{path}\导出主数据\{profitCenterGroup}.txt"
    if os.path.exists(filePath):  # 判断文件是否存在
        try:
            os.remove(filePath)  # 尝试删除文件
        except Exception as e:
            print(f"删除文件时发生错误: {e}")
    if not os.path.exists(f"{path}/导出主数据"):
            os.makedirs(f"{path}/导出主数据")
    session.findById("wnd[1]/usr/ctxtDY_PATH").text =f"{path}/导出主数据"
    session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = f"{profitCenterGroup}.txt"

    session.findById("wnd[1]/usr/ctxtDY_FILENAME").caretPosition = 7
    session.findById("wnd[1]/tbar[0]/btn[0]").press()
    
    pathList.append(filePath)
def exportinternalTransaction(session,lock,ifDict,path,pathList:list):
    if not os.path.exists(f"{path}/导出内部对账数据"):
            os.makedirs(f"{path}/导出内部对账数据")
    session.StartTransaction("ZARAP0014")
    session.findById("wnd[0]/usr/ctxtS_ARAPT-LOW").text = "ARAP01"
    session.findById("wnd[0]/usr/btn%_S_PRCTR_%_APP_%-VALU_PUSH").press()
    session.findById("wnd[1]/tbar[0]/btn[24]").press()
    session.findById("wnd[1]/tbar[0]/btn[8]").press()
    session.findById("wnd[0]/tbar[1]/btn[8]").press()
    tableRowCount = session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").RowCount
    for i in range(tableRowCount):
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(i, "PRCTR")
        name1=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").getCellValue(i,"PRCTR")
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(i, "D_PARTNER")
        name2=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").getCellValue(i,"D_PARTNER")
        with lock:
            if name1+"+"+name2 not in ifDict:
                ifContine=True
                ifDict[name1+"+"+name2]=1
            else:
                ifContine=False
        if ifContine:      
            filePath = f"{path}\导出内部对账数据\{name1}+{name2}.txt"
            if os.path.exists(filePath):  # 判断文件是否存在
                try:
                    os.remove(filePath)  # 尝试删除文件
                except Exception as e:
                    print(f"删除文件时发生错误: {e}")
            session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(i, "D_HSL_LJ")
            session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").doubleClickCurrentCell()
            try:
                session.findById("wnd[0]/shellcont/shell/shellcont[1]/shell").pressToolbarContextButton("&MB_EXPORT")
                session.findById("wnd[0]/shellcont/shell/shellcont[1]/shell").selectContextMenuItem("&PC")
                session.findById("wnd[1]/tbar[0]/btn[0]").press()
                session.findById("wnd[1]/usr/ctxtDY_PATH").text =f"{path}/导出内部对账数据"
                session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = f"{name1}+{name2}.txt"
                session.findById("wnd[1]/usr/ctxtDY_FILENAME").caretPosition = 7
                session.findById("wnd[1]/tbar[0]/btn[0]").press()
            except:
                pass
            session.findById("wnd[0]/tbar[0]/btn[3]").press()
            pathList.append(filePath)
def exportinternal(session,profit,path,pathList:list):
    session.StartTransaction("ZARAP0014")
    session.findById("wnd[0]/usr/ctxtS_ARAPT-LOW").text = "ARAP01"
    session.findById("wnd[0]/usr/ctxtS_PRCTR-LOW").text = "L306200232"
    session.findById("wnd[0]/usr/ctxtS_PRCTR-LOW").setFocus()
    session.findById("wnd[0]/usr/ctxtS_PRCTR-LOW").caretPosition = 10
    session.findById("wnd[0]/tbar[1]/btn[8]").press()
    tableRowCount = session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").RowCount
    for i in range(tableRowCount):
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(i, "PARTNER")
        name1=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").getCellValue(i,"PARTNER")
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(i, "D_PRCTR")
        name2=session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").getCellValue(i,"D_PRCTR")  
        filePath = f"{path}\导出内部对账数据\{name1}+{name2}.txt"
        if os.path.exists(filePath):  # 判断文件是否存在
            try:
                os.remove(filePath)  # 尝试删除文件
            except Exception as e:
                print(f"删除文件时发生错误: {e}")
        if not os.path.exists(f"{path}/导出内部对账数据"):
            os.makedirs(f"{path}/导出内部对账数据")
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").setCurrentCell(i, "D_HSL_LJ")
        session.findById("wnd[0]/usr/cntlGRID1/shellcont/shell").doubleClickCurrentCell()
        try:
            session.findById("wnd[0]/shellcont/shell/shellcont[1]/shell").pressToolbarContextButton("&MB_EXPORT")
            session.findById("wnd[0]/shellcont/shell/shellcont[1]/shell").selectContextMenuItem("&PC")
            session.findById("wnd[1]/tbar[0]/btn[0]").press()
            session.findById("wnd[1]/usr/ctxtDY_PATH").text =f"{path}/导出内部对账数据"
            session.findById("wnd[1]/usr/ctxtDY_FILENAME").text = f"{name1}+{name2}.txt"
            session.findById("wnd[1]/usr/ctxtDY_FILENAME").caretPosition = 7
            session.findById("wnd[1]/tbar[0]/btn[0]").press()
        except:
            pass
        session.findById("wnd[0]/tbar[0]/btn[3]").press()
        pathList.append(filePath)
def exportSapData(sessionIndex,ifDict:dict,params:list,theLock:threading.Lock,startDate,endDate,path,pathList:list,businessType,detailDownloadStatusList,onlyPayable=False):
    pythoncom.CoInitialize()
    td=70
    #连接指定索引sap会话
    while td>0:
        try:
            SapGuiAuto = win32com.client.GetObject("SAPGUI")
            application = SapGuiAuto.GetScriptingEngine
            connection = application.Children(0)
            session=connection.Children(sessionIndex)
            print(f"线程{sessionIndex}开始")
            td=-1
        except:
            print("出错")
            time.sleep(1)
            td=td-1
    for paramsRow in range(len(params)):
        if startDate!=None:
            if businessType=="导出明细数据" :
                dateList=getDateList(startDate,endDate) 
            else:
                dateList=[startDate,endDate]
            for i in range(1,len(dateList)):
                start_date=dateList[i-1]
                end_date=dateList[i]
                with theLock:
                    companyCode=params[paramsRow][1]
                    profitCenterGroup=params[paramsRow][0]
                    if params[paramsRow][0]+start_date+end_date not in ifDict and params[paramsRow][2]=="是":
                        ifContinue=True
                        ifDict[params[paramsRow][0]+start_date+end_date]=True
                    else:
                        ifContinue=False
                if ifContinue:
                    if businessType=="导出科目余额表":
                        exportBalanceTable(session,companyCode,profitCenterGroup,start_date,end_date,path,pathList)
                    elif businessType=="导出明细数据":
                        successStatus=exportDetails(session,companyCode,profitCenterGroup,start_date,end_date,path,pathList,onlyPayable)
                        if successStatus:
                            therow=[companyCode,profitCenterGroup,start_date,end_date,"成功"]
                        else:
                            therow=[companyCode,profitCenterGroup,start_date,end_date,"失败"]
                        detailDownloadStatusList.append(therow)
        else:
            if businessType=="导出主数据":
                with theLock:
                    companyCode=params[paramsRow][1]
                    profitCenterGroup=params[paramsRow][0]
                    if params[paramsRow][0] not in ifDict and params[paramsRow][2]=="是":
                        ifContinue=True
                        ifDict[params[paramsRow][0]]=True
                    else:
                        ifContinue=False
                if ifContinue:
                    exportBaseData(session,companyCode,profitCenterGroup,path,pathList) 
            elif businessType=="导出内部对账2":
                with theLock:
                    profitCode=params[paramsRow][0]
                    if params[paramsRow][0] not in ifDict:
                        ifContinue=True
                        ifDict[params[paramsRow][0]]=True
                    else:
                        ifContinue=False
                if ifContinue:
                    exportinternal(session,profitCode,path,pathList) 
    #上面嵌套太多
    if businessType=="按excel模板导出明细数据":
        for i in range(1,len(params)):
            with theLock:
                companyCode=params[i][3]
                profitCenterGroup=params[i][2]
                start_date=params[i][0]
                end_date=params[i][1]
                if params[i][4] == "是":
                    ifContinue=True
                    params[i][4]="否"
                else:
                    ifContinue=False
            if ifContinue:
                exportDetails(session,companyCode,profitCenterGroup,start_date,end_date,path,pathList,onlyPayable)


    if businessType=="导出内部对账1":
        exportinternalTransaction(session,theLock,ifDict,path,pathList)
    session.findById("wnd[0]").close() #关闭放在外面
    try:
        session.findById("wnd[1]/usr/btnSPOP-OPTION1").press()
    except Exception as e:
        pass  

def getDateList(startDate,endDate):
    start_date = datetime.strptime(startDate, "%Y-%m-%d")
    end_date = datetime.strptime(endDate, "%Y-%m-%d")
    
    # 计算两个日期之间的天数差
    delta = (end_date - start_date).days
    
    # 如果两个日期之间的天数差大于90天，则以90天为区间生成日期列表
    if delta > 90:
        date_list = []
        current_date = start_date
        while current_date < end_date:
            date_list.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=90)
        # 添加最后一个日期
        date_list.append(end_date.strftime("%Y-%m-%d"))
        return date_list
    else:
        # 如果两个日期之间的天数差小于等于90天，则返回一个包含两个日期的列表
        return [startDate, endDate]
def exportMain(exportContent,startDate=None,endDate=None,onlyPayable=False):


    ifDict={}#创建冲突字典，防止重复导出
    params=[]#创建参数列表，用于传递给导出函数
    filePaths=[] #创建返回值变量，返回所有导出文件路径
    detailDownloadStatusList=[] #创建明细下载状态列表，用于记录导出状态

    if "导出内部对账" in exportContent: #将内部对账的利润中心上传到剪切板，再传入sap
        con = duckdb.connect(database=settings.PATH_DUCKDB+'/example.duckdb', read_only=True)
        params=con.execute("select 利润中心 from 主数据").fetchall()
        import win32clipboard
        # 将列表转换为字符串
        list_str=[ele[0] for ele in params]
        list_str2 = "\r\n".join(list_str)
        win32clipboard.OpenClipboard()
        win32clipboard.EmptyClipboard()
        win32clipboard.SetClipboardText(list_str2)
        win32clipboard.CloseClipboard()
    else:
        params=configDB().sapProfitCenter
        print(params)

    if "按excel模板导出明细数据" in exportContent:
        params=excel.myBook().sheet("导入面板").usedrange

    savePath=settings.PATH_DOWNLOAD
    threads = []
    lock=threading.Lock()
    threadCount=2 if "导出内部对账" in exportContent else 5
    GetSAPSession.creatSAP()
    GetSAPSession.connect_SAP(threadCount) #打开sap并创建5个sap会话
    time.sleep(3)
    for i in range(threadCount):
        #ifDict:dict,params:list,theLock:threading.Lock,startDate,endDate,path,pathList:list,businessType
        threads.append(threading.Thread(target=exportSapData,args=(i,ifDict,params,lock,startDate,endDate,savePath,filePaths,exportContent,detailDownloadStatusList,onlyPayable)))
    for t in threads:
        t.daemon = True
        t.start()
    for t in threads:
        t.join()  #等待线程结束

    #明细导出状态
    if exportContent=="导出明细数据":
        group_map={}
        for row in params:
            group_map[row[0]]=row[3]
        import openpyxl
        wb = openpyxl.Workbook()
        ws = wb.active
        row=["公司代码","利润中心组","开始日期","结束日期","状态","利润中心组名称"]
        ws.append(row)
        for row in detailDownloadStatusList:
            row.append(group_map[row[1]])
            ws.append(row)
        wb.save(settings.PATH_EXCEL+f"/导出明细数据状态.xlsx")
        print("下载情况已导出"+settings.PATH_EXCEL+f"/导出明细数据状态.xlsx")
    #导出明细数据状态

    return filePaths